#!/usr/bin/env python3
"""
Test script to demonstrate enhanced node properties for GraphRAG.
"""

import os
import sys
from pprint import pprint

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from constants.entities import get_entity_properties, EntityType


def test_entity_properties():
    """Test the enhanced entity properties functionality."""
    print("🧪 Testing Enhanced Entity Properties for GraphRAG")
    print("=" * 60)
    
    # Test different entity types
    test_entities = [
        "Person",
        "Manager", 
        "Company",
        "Project",
        "System",
        "Document",
        "Office",
        "Goal"
    ]
    
    for entity_type in test_entities:
        print(f"\n📋 Entity Type: {entity_type}")
        print("-" * 40)
        
        properties = get_entity_properties(entity_type)
        
        # Display key properties for GraphRAG
        print(f"Description: {properties.get('description', 'N/A')}")
        print(f"Category: {properties.get('category', 'N/A')}")
        print(f"Graph Importance: {properties.get('graph_importance', 'N/A')}")
        
        # Show GraphRAG-relevant properties
        if properties.get('is_human'):
            print("🧑 Human entity - can have employment relationships")
        if properties.get('is_organization'):
            print("🏢 Organization - can employ people and have departments")
        if properties.get('is_technology'):
            print("💻 Technology - can be integrated with other systems")
        if properties.get('leadership_role'):
            print("👑 Leadership role - likely to manage others")
        if properties.get('contains_knowledge'):
            print("📚 Contains knowledge - valuable for information retrieval")
        
        # Show typical relationships for GraphRAG queries
        typical_rels = properties.get('typical_relationships', [])
        if typical_rels:
            print(f"🔗 Typical relationships: {', '.join(typical_rels[:3])}...")
        
        # Show context keywords for search
        keywords = properties.get('context_keywords', [])
        if keywords:
            print(f"🔍 Search keywords: {', '.join(keywords[:3])}...")


def test_graphrag_scenarios():
    """Test how enhanced properties benefit GraphRAG scenarios."""
    print("\n\n🎯 GraphRAG Benefit Scenarios")
    print("=" * 60)
    
    scenarios = [
        {
            "query": "Find all managers in the organization",
            "entity_type": "Manager",
            "benefit": "leadership_role=True helps identify management hierarchy"
        },
        {
            "query": "What systems can be integrated?",
            "entity_type": "System", 
            "benefit": "can_be_integrated=True + typical_relationships help find integration points"
        },
        {
            "query": "Who are the key people in projects?",
            "entity_type": "Person",
            "benefit": "graph_importance score helps rank by centrality"
        },
        {
            "query": "Find knowledge sources",
            "entity_type": "Document",
            "benefit": "contains_knowledge=True identifies information-rich nodes"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. Scenario: {scenario['query']}")
        print(f"   Entity Type: {scenario['entity_type']}")
        print(f"   GraphRAG Benefit: {scenario['benefit']}")
        
        # Show the actual properties
        props = get_entity_properties(scenario['entity_type'])
        relevant_props = {k: v for k, v in props.items() 
                         if k in ['graph_importance', 'leadership_role', 'can_be_integrated', 
                                'contains_knowledge', 'typical_relationships']}
        print(f"   Properties: {relevant_props}")


def test_neo4j_query_examples():
    """Show example Neo4j queries that benefit from enhanced properties."""
    print("\n\n🔍 Enhanced Neo4j Queries for GraphRAG")
    print("=" * 60)
    
    queries = [
        {
            "name": "Find High-Importance Entities",
            "query": """
            MATCH (n)
            WHERE n.graph_importance > 0.8
            RETURN n.name, n.entity_type, n.graph_importance
            ORDER BY n.graph_importance DESC
            """,
            "benefit": "Prioritizes central entities for GraphRAG responses"
        },
        {
            "name": "Find Leadership Network",
            "query": """
            MATCH (leader)
            WHERE leader.leadership_role = true
            MATCH (leader)-[r:MANAGES|LEADS]->(subordinate)
            RETURN leader.name, type(r), subordinate.name
            """,
            "benefit": "Quickly identifies organizational hierarchy"
        },
        {
            "name": "Find Integration Opportunities", 
            "query": """
            MATCH (tech)
            WHERE tech.is_technology = true AND tech.can_be_integrated = true
            MATCH (tech)-[r:INTEGRATES_WITH|CONNECTS_TO]-(other)
            RETURN tech.name, other.name, type(r)
            """,
            "benefit": "Identifies technology integration patterns"
        },
        {
            "name": "Find Knowledge Sources by Category",
            "query": """
            MATCH (doc)
            WHERE doc.contains_knowledge = true
            RETURN doc.category, collect(doc.name) as documents
            ORDER BY doc.category
            """,
            "benefit": "Organizes knowledge sources for targeted retrieval"
        },
        {
            "name": "Search by Context Keywords",
            "query": """
            MATCH (n)
            WHERE any(keyword IN n.context_keywords WHERE keyword CONTAINS $search_term)
            RETURN n.name, n.entity_type, n.context_keywords
            """,
            "benefit": "Enables semantic search across entity descriptions"
        }
    ]
    
    for i, query_info in enumerate(queries, 1):
        print(f"\n{i}. {query_info['name']}")
        print(f"   Benefit: {query_info['benefit']}")
        print(f"   Query: {query_info['query'].strip()}")


def main():
    """Run all tests."""
    test_entity_properties()
    test_graphrag_scenarios()
    test_neo4j_query_examples()
    
    print("\n\n✅ Enhanced Properties Summary")
    print("=" * 60)
    print("🎯 GraphRAG Benefits:")
    print("   • Rich semantic context for better entity understanding")
    print("   • Graph importance scoring for result prioritization") 
    print("   • Category-based organization for structured queries")
    print("   • Relationship hints for traversal optimization")
    print("   • Search keywords for semantic matching")
    print("   • Type-specific flags for targeted filtering")
    print("\n💡 Next Steps:")
    print("   • Process documents to see enhanced nodes in Neo4j")
    print("   • Use graph_importance for GraphRAG ranking")
    print("   • Leverage categories for organized knowledge retrieval")
    print("   • Utilize context_keywords for semantic search")


if __name__ == "__main__":
    main()
