"""
Test script for Requesty integration with Enterprise KG Minimal

This script tests all components to ensure they work correctly with the Requesty API.
"""

import os
import logging
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_environment_variables():
    """Test that all required environment variables are set."""
    print("\n" + "="*60)
    print("TESTING ENVIRONMENT VARIABLES")
    print("="*60)
    
    required_vars = {
        'REQUESTY_API_KEY': os.getenv('REQUESTY_API_KEY'),
        'REQUESTY_BASE_URL': os.getenv('REQUESTY_BASE_URL'),
        'LLM_PROVIDER': os.getenv('LLM_PROVIDER'),
        'LLM_MODEL': os.getenv('LLM_MODEL'),
        'NEO4J_URI': os.getenv('NEO4J_URI'),
        'NEO4J_USER': os.getenv('NEO4J_USER'),
        'NEO4J_PASSWORD': os.getenv('NEO4J_PASSWORD')
    }
    
    all_set = True
    for var_name, var_value in required_vars.items():
        if var_value:
            # Mask sensitive values
            if 'KEY' in var_name or 'PASSWORD' in var_name:
                display_value = f"{var_value[:10]}...{var_value[-4:]}" if len(var_value) > 14 else "***"
            else:
                display_value = var_value
            print(f"✅ {var_name}: {display_value}")
        else:
            print(f"❌ {var_name}: Not set")
            all_set = False
    
    return all_set


def test_llm_client():
    """Test the LLM client with Requesty."""
    print("\n" + "="*60)
    print("TESTING LLM CLIENT WITH REQUESTY")
    print("="*60)
    
    try:
        from standalone_processor import LLMClient
        
        # Create LLM client with Requesty
        llm_client = LLMClient(
            provider=os.getenv('LLM_PROVIDER', 'requesty'),
            model=os.getenv('LLM_MODEL', 'openai/gpt-4o'),
            api_key=os.getenv('REQUESTY_API_KEY')
        )
        
        print(f"✅ LLM Client created successfully")
        print(f"   Provider: {llm_client.provider}")
        print(f"   Model: {llm_client.model}")
        print(f"   API Key: {'Set' if llm_client.api_key else 'Not set'}")
        print(f"   Client initialized: {'Yes' if llm_client.client else 'No'}")
        
        # Test a simple structured response
        test_prompt = """
        Extract entities and relationships from this text:
        "John Smith is the project manager for Project Alpha. He works at TechCorp and reports to Sarah Johnson."
        """
        
        schema_description = """
        [
            {
                "source_entity": "string - name of source entity",
                "target_entity": "string - name of target entity", 
                "relationship_type": "string - type of relationship",
                "fact": "string - supporting fact from text"
            }
        ]
        """
        
        print("\n🔄 Testing LLM response...")
        response = llm_client.generate_structured_response(test_prompt, schema_description)
        
        if response:
            print("✅ LLM response received successfully")
            print(f"   Response type: {type(response)}")
            if isinstance(response, list) and len(response) > 0:
                print(f"   Number of relationships: {len(response)}")
                print(f"   Sample relationship: {response[0] if response else 'None'}")
            else:
                print(f"   Response content: {response}")
            return True
        else:
            print("❌ No response received from LLM")
            return False
            
    except Exception as e:
        print(f"❌ LLM Client test failed: {e}")
        logger.error(f"LLM Client test error: {e}", exc_info=True)
        return False


def test_neo4j_connection():
    """Test Neo4j database connection."""
    print("\n" + "="*60)
    print("TESTING NEO4J CONNECTION")
    print("="*60)
    
    try:
        from storage.neo4j_client import Neo4jClient, Neo4jConnection
        
        # Create Neo4j connection
        neo4j_conn = Neo4jConnection(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD')
        )
        
        neo4j_client = Neo4jClient(neo4j_conn)
        
        print("✅ Neo4j client created successfully")
        
        # Test connection with a simple query
        test_query = "RETURN 'Connection test successful' as message"
        result = neo4j_client.execute_query(test_query)
        
        if result:
            print("✅ Neo4j connection test successful")
            print(f"   Result: {list(result)[0]['message']}")
            return True
        else:
            print("❌ Neo4j connection test failed - no result")
            return False
            
    except Exception as e:
        print(f"❌ Neo4j connection test failed: {e}")
        logger.error(f"Neo4j connection error: {e}", exc_info=True)
        return False


def test_standalone_processor():
    """Test the complete standalone processor."""
    print("\n" + "="*60)
    print("TESTING STANDALONE PROCESSOR")
    print("="*60)
    
    try:
        from standalone_processor import LLMClient, StandaloneDocumentProcessor
        from storage.neo4j_client import Neo4jClient, Neo4jConnection
        
        # Create LLM client
        llm_client = LLMClient(
            provider=os.getenv('LLM_PROVIDER', 'requesty'),
            model=os.getenv('LLM_MODEL', 'openai/gpt-4o')
        )
        
        # Create Neo4j client
        neo4j_conn = Neo4jConnection(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD')
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        # Create processor
        processor = StandaloneDocumentProcessor(
            llm_client=llm_client,
            neo4j_client=neo4j_client,
            enable_summarization=True
        )
        
        print("✅ Standalone processor created successfully")
        
        # Create a test document
        test_content = """
        # Project Alpha Status Report
        
        ## Team Members
        - John Smith: Project Manager
        - Sarah Johnson: Lead Developer  
        - Mike Wilson: UI Designer
        - Lisa Chen: Data Analyst
        
        ## Project Overview
        Project Alpha is a new CRM system being developed by TechCorp. 
        John Smith manages the project and reports to Sarah Johnson.
        The team is working on integrating the CRM with the Analytics Dashboard.
        
        ## Systems Integration
        The CRM System will integrate with:
        - Analytics Dashboard (managed by Lisa Chen)
        - Mobile App (designed by Mike Wilson)
        - Database Server (maintained by IT Department)
        
        ## Current Status
        The project is 60% complete and on track for Q2 delivery.
        """
        
        # Save test content to a temporary file
        test_file_path = "test_document.md"
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📄 Created test document: {test_file_path}")
        
        # Process the document
        print("🔄 Processing test document...")
        metadata = processor.process_document(test_file_path)
        
        # Clean up test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        
        # Check results
        if metadata.errors:
            print(f"❌ Processing failed with errors: {metadata.errors}")
            return False
        else:
            print("✅ Document processed successfully")
            print(f"   Document ID: {metadata.document_id}")
            print(f"   File size: {metadata.file_size_bytes} bytes")
            print(f"   Processing time: {metadata.processing_duration_seconds:.2f}s")
            print(f"   Summarization: {'✅' if metadata.summarization_completed else '❌'}")
            print(f"   Entity extraction: {'✅' if metadata.entity_extraction_completed else '❌'}")
            print(f"   Relationship extraction: {'✅' if metadata.relationship_extraction_completed else '❌'}")
            print(f"   Graph storage: {'✅' if metadata.graph_storage_completed else '❌'}")
            return True
            
    except Exception as e:
        print(f"❌ Standalone processor test failed: {e}")
        logger.error(f"Standalone processor error: {e}", exc_info=True)
        return False


def test_graph_query():
    """Test querying the knowledge graph."""
    print("\n" + "="*60)
    print("TESTING KNOWLEDGE GRAPH QUERIES")
    print("="*60)
    
    try:
        from storage.neo4j_client import Neo4jClient, Neo4jConnection
        
        # Create Neo4j client
        neo4j_conn = Neo4jConnection(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD')
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        # Query for entities
        entity_query = """
        MATCH (e:Entity)
        RETURN e.name as name, e.type as type, labels(e) as labels
        LIMIT 10
        """
        
        entities = neo4j_client.execute_query(entity_query)
        entity_list = list(entities)
        
        print(f"✅ Found {len(entity_list)} entities in the graph")
        for entity in entity_list[:5]:  # Show first 5
            print(f"   - {entity['name']} ({entity['type']})")
        
        # Query for relationships
        relationship_query = """
        MATCH (source:Entity)-[r]->(target:Entity)
        RETURN source.name as source, type(r) as relationship, target.name as target
        LIMIT 10
        """
        
        relationships = neo4j_client.execute_query(relationship_query)
        relationship_list = list(relationships)
        
        print(f"✅ Found {len(relationship_list)} relationships in the graph")
        for rel in relationship_list[:5]:  # Show first 5
            print(f"   - {rel['source']} --[{rel['relationship']}]--> {rel['target']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Graph query test failed: {e}")
        logger.error(f"Graph query error: {e}", exc_info=True)
        return False


def main():
    """Run all tests."""
    print("🚀 Enterprise KG Minimal - Requesty Integration Test")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    test_results = []
    
    # Run all tests
    test_results.append(("Environment Variables", test_environment_variables()))
    test_results.append(("LLM Client", test_llm_client()))
    test_results.append(("Neo4j Connection", test_neo4j_connection()))
    test_results.append(("Standalone Processor", test_standalone_processor()))
    test_results.append(("Graph Queries", test_graph_query()))
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<50} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal tests: {len(test_results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! Requesty integration is working correctly.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the configuration and logs.")
    
    print(f"Test completed at: {datetime.now()}")


if __name__ == "__main__":
    main()
