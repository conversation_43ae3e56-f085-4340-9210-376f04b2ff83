#!/usr/bin/env python3
"""
Test script to verify .docx support in the enterprise KG system.
"""

import os
import sys
import tempfile
from docx import Document

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from standalone_processor import StandaloneDocumentProcessor


def create_test_docx(file_path: str):
    """Create a test .docx file with sample content."""
    doc = Document()
    
    # Add title
    title = doc.add_heading('Enterprise Project Report', 0)
    
    # Add paragraphs
    doc.add_paragraph('This is a test document for the Enterprise Knowledge Graph system.')
    
    doc.add_paragraph('<PERSON> is the project manager for the Alpha Project. '
                     'He works for Acme Corporation and manages a team of developers.')
    
    doc.add_paragraph('<PERSON> is involved in the Beta Initiative and reports to <PERSON>. '
                     'She mentions that the project timeline needs to be reviewed.')
    
    # Add a table
    table = doc.add_table(rows=1, cols=3)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Name'
    hdr_cells[1].text = 'Role'
    hdr_cells[2].text = 'Project'
    
    row_cells = table.add_row().cells
    row_cells[0].text = '<PERSON>'
    row_cells[1].text = 'Developer'
    row_cells[2].text = 'Gamma System'
    
    row_cells = table.add_row().cells
    row_cells[0].text = 'Lisa Chen'
    row_cells[1].text = 'Designer'
    row_cells[2].text = 'Delta Platform'
    
    # Save the document
    doc.save(file_path)
    print(f"✓ Created test .docx file: {file_path}")


def test_docx_reading():
    """Test reading .docx files."""
    print("🧪 Testing .docx reading functionality...")
    
    # Create a temporary .docx file
    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp_file:
        docx_path = tmp_file.name
    
    try:
        # Create test document
        create_test_docx(docx_path)
        
        # Create a minimal processor instance for testing
        class TestProcessor:
            def _read_docx(self, file_path: str) -> str:
                """Test the _read_docx method."""
                try:
                    from docx import Document

                    doc = Document(file_path)
                    text_content = []

                    # Extract text from paragraphs
                    for paragraph in doc.paragraphs:
                        if paragraph.text.strip():
                            text_content.append(paragraph.text)

                    # Extract text from tables
                    for table in doc.tables:
                        for row in table.rows:
                            row_text = []
                            for cell in row.cells:
                                if cell.text.strip():
                                    row_text.append(cell.text.strip())
                            if row_text:
                                text_content.append(' | '.join(row_text))

                    return '\n'.join(text_content)

                except ImportError:
                    print("python-docx not installed. Install with: pip install python-docx")
                    raise
                except Exception as e:
                    print(f"Failed to read DOCX {file_path}: {e}")
                    raise
        
        # Test reading
        processor = TestProcessor()
        content = processor._read_docx(docx_path)
        
        print("📄 Extracted content:")
        print("-" * 50)
        print(content)
        print("-" * 50)
        
        # Verify content contains expected elements
        expected_elements = [
            "Enterprise Project Report",
            "John Smith",
            "Alpha Project",
            "Acme Corporation",
            "Sarah Johnson",
            "Beta Initiative",
            "Mike Davis",
            "Lisa Chen"
        ]
        
        missing_elements = []
        for element in expected_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing expected elements: {missing_elements}")
            return False
        else:
            print("✅ All expected elements found in extracted content!")
            return True
            
    finally:
        # Clean up
        if os.path.exists(docx_path):
            os.unlink(docx_path)
            print(f"🗑️  Cleaned up test file: {docx_path}")


def test_file_extension_detection():
    """Test that .docx files are properly detected."""
    print("\n🧪 Testing file extension detection...")
    
    class TestProcessor:
        def _read_document(self, file_path: str) -> str:
            """Test the _read_document method."""
            file_extension = os.path.splitext(file_path)[1].lower()
            
            if file_extension == '.pdf':
                return "PDF content"
            elif file_extension == '.docx':
                return "DOCX content"
            else:
                return "Text content"
    
    processor = TestProcessor()
    
    # Test different file extensions
    test_cases = [
        ("test.docx", "DOCX content"),
        ("test.DOCX", "DOCX content"),
        ("test.pdf", "PDF content"),
        ("test.txt", "Text content"),
        ("test.md", "Text content")
    ]
    
    all_passed = True
    for filename, expected in test_cases:
        result = processor._read_document(filename)
        if result == expected:
            print(f"✅ {filename} -> {result}")
        else:
            print(f"❌ {filename} -> {result} (expected: {expected})")
            all_passed = False
    
    return all_passed


def main():
    """Run all tests."""
    print("🚀 Testing .docx Support for Enterprise KG")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: .docx reading
    if test_docx_reading():
        tests_passed += 1
    
    # Test 2: File extension detection
    if test_file_extension_detection():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! .docx support is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
