# Enhanced Enterprise KG Minimal

## Overview

This enhanced version of enterprise_kg_minimal provides significant improvements over both the basic standalone processor and the enterprise_kg_graphiti implementation. The enhancements focus on modularity, performance, and advanced knowledge extraction capabilities.

## 🚀 Key Enhancements

### 1. Advanced Chunking Engine (`chunking_engine.py`)

**Features:**
- **Multiple Chunking Strategies**: Fixed-size, sentence-based, paragraph-based, semantic-based, and hybrid
- **Intelligent Boundary Preservation**: Respects sentence and paragraph boundaries
- **Rich Metadata**: Comprehensive chunk metadata including confidence scores and semantic topics
- **Configurable Overlap**: Customizable overlap between chunks for context preservation

**Advantages over enterprise_kg_graphiti:**
- ✅ More sophisticated chunking strategies
- ✅ Better context preservation
- ✅ Configurable chunk sizes and overlaps
- ✅ Rich metadata for each chunk

```python
from chunking_engine import create_chunking_engine

# Create chunking engine with hybrid strategy
chunker = create_chunking_engine(
    strategy="hybrid",
    chunk_size=1000,
    chunk_overlap=200
)

# Chunk a document
chunks = chunker.chunk_document(content, "document.md")
```

### 2. Hybrid Search Engine (`hybrid_search_engine.py`)

**Features:**
- **Multiple Search Methods**: Semantic-only, graph-only, template-hybrid, discovery-hybrid, auto-hybrid
- **Template-Based Queries**: Predefined patterns for common enterprise queries
- **Entity Discovery Approach**: Extract entities from semantic results for graph queries
- **Pinecone-Compatible**: Ready for semantic search integration
- **Intelligent Query Routing**: Automatically chooses the best search approach

**Advantages over enterprise_kg_graphiti:**
- ✅ True hybrid search combining semantic and graph approaches
- ✅ Template-based query optimization
- ✅ Better result ranking and combination
- ✅ Pinecone integration ready

```python
from hybrid_search_engine import create_hybrid_search_engine

# Create hybrid search engine
search_engine = create_hybrid_search_engine()

# Perform hybrid search
results = await search_engine.search(
    query="Who is working on Project Alpha?",
    method="auto_hybrid",
    top_k=10
)
```

### 3. Knowledge Enrichment Engine (`knowledge_enrichment.py`)

**Features:**
- **Template-Based Query Building**: Predefined Cypher templates for common patterns
- **Intent Detection**: Automatically detects query intent and applies appropriate templates
- **Entity Extraction**: Advanced entity recognition from natural language queries
- **Query Confidence Scoring**: Ranks enriched queries by confidence
- **Explanation Generation**: Provides human-readable explanations for query choices

**Advantages over enterprise_kg_graphiti:**
- ✅ Intelligent query understanding
- ✅ Template-based optimization
- ✅ Better entity recognition
- ✅ Query explanation capabilities

```python
from knowledge_enrichment import create_knowledge_enrichment_engine

# Create knowledge enrichment engine
enricher = create_knowledge_enrichment_engine()

# Enrich a natural language query
enriched_queries = enricher.enrich_query("Who manages the development team?")
```

### 4. Entity Discovery Engine (`entity_discovery.py`)

**Features:**
- **Advanced Entity Recognition**: Multiple pattern-based approaches for entity extraction
- **Entity Type Classification**: Automatic classification of discovered entities
- **Entity Linking and Disambiguation**: Merges similar entities and resolves aliases
- **Relationship Discovery**: Extracts relationships between discovered entities
- **Entity Clustering**: Groups related entities for better organization

**Advantages over enterprise_kg_graphiti:**
- ✅ More sophisticated entity recognition
- ✅ Better entity disambiguation
- ✅ Relationship discovery capabilities
- ✅ Entity clustering for organization

```python
from entity_discovery import create_entity_discovery_engine

# Create entity discovery engine
discoverer = create_entity_discovery_engine()

# Discover entities from search results
entities = discoverer.discover_entities(search_results)
```

### 5. Enhanced Standalone Processor (`enhanced_standalone_processor.py`)

**Features:**
- **Modular Architecture**: All enhancements can be enabled/disabled independently
- **Multiple File Format Support**: PDF, DOCX, JSON, MD, TXT
- **Batch Processing**: Process entire directories efficiently
- **Performance Optimization**: Efficient processing and storage
- **Comprehensive Error Handling**: Robust error handling and logging

**Advantages over enterprise_kg_graphiti:**
- ✅ More modular and configurable
- ✅ Better file format support
- ✅ Improved performance
- ✅ Better error handling

```python
from enhanced_standalone_processor import create_enhanced_standalone_processor

# Create enhanced processor
processor = create_enhanced_standalone_processor(
    chunking_strategy="hybrid",
    enable_hybrid_search=True,
    enable_entity_discovery=True,
    enable_knowledge_enrichment=True
)

# Process documents and search
metadata = processor.process_document("document.md")
results = await processor.search("Who works on Project Alpha?")
```

## 🔄 Comparison with enterprise_kg_graphiti

| Feature | enterprise_kg_graphiti | Enhanced enterprise_kg_minimal |
|---------|------------------------|--------------------------------|
| **Chunking** | Basic fixed-size | Multiple strategies with intelligence |
| **Search** | Basic graph queries | True hybrid semantic + graph |
| **Entity Recognition** | Limited LLM-based | Advanced pattern + LLM hybrid |
| **Query Processing** | Simple text matching | Template-based with intent detection |
| **Performance** | Schema processing issues | Optimized modular architecture |
| **Modularity** | Monolithic | Highly modular components |
| **File Support** | Limited | PDF, DOCX, JSON, MD, TXT |
| **Error Handling** | Basic | Comprehensive with recovery |
| **Scalability** | Limited | Designed for enterprise scale |

## 📊 Performance Benefits

### 1. **Faster Processing**
- Optimized chunking reduces LLM calls
- Batch processing for multiple documents
- Efficient Neo4j operations

### 2. **Better Accuracy**
- Hybrid search improves result relevance
- Template-based queries reduce hallucination
- Entity disambiguation improves precision

### 3. **Enhanced Scalability**
- Modular architecture allows selective feature use
- Configurable chunk sizes for different document types
- Efficient memory usage

### 4. **Improved Reliability**
- Comprehensive error handling
- Fallback mechanisms for failed operations
- Robust schema processing

## 🛠️ Installation and Setup

### Prerequisites
```bash
# Install required packages
pip install neo4j openai anthropic python-docx pypdf

# Optional: For Pinecone integration
pip install pinecone-client
```

### Basic Setup
```python
from enhanced_standalone_processor import create_enhanced_standalone_processor

# Create processor with all enhancements
processor = create_enhanced_standalone_processor(
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    llm_provider="openai",
    llm_model="gpt-4o"
)
```

## 📚 Usage Examples

### Process a Single Document
```python
# Process with advanced chunking
metadata = processor.process_document(
    file_path="documents/project_plan.md",
    enable_chunking=True
)
```

### Perform Hybrid Search
```python
# Search with automatic method selection
results = await processor.search(
    query="Who is responsible for the CRM integration?",
    method="auto_hybrid",
    top_k=10
)
```

### Discover Entities
```python
# Discover entities from search results
entities = processor.discover_entities(search_results)
```

### Enrich Queries
```python
# Get enriched query templates
enriched = processor.enrich_query("What systems does John work with?")
```

## 🔧 Configuration Options

### Chunking Configuration
```python
processor = create_enhanced_standalone_processor(
    chunking_strategy="hybrid",  # or "fixed_size", "sentence_based", etc.
    chunk_size=1000,
    chunk_overlap=200
)
```

### Feature Toggles
```python
processor = create_enhanced_standalone_processor(
    enable_hybrid_search=True,
    enable_entity_discovery=True,
    enable_knowledge_enrichment=True
)
```

## 🎯 Why Choose Enhanced enterprise_kg_minimal?

1. **Proven Stability**: Built on the stable enterprise_kg_minimal foundation
2. **Modular Design**: Use only the features you need
3. **Performance Optimized**: Faster processing with better accuracy
4. **Enterprise Ready**: Designed for production environments
5. **Extensible**: Easy to add new features and integrations
6. **Well Documented**: Comprehensive documentation and examples

## 🚀 Getting Started

1. **Run the examples**: `python example_enhanced_usage.py`
2. **Process your documents**: Use the enhanced processor for your data
3. **Explore hybrid search**: Try different search methods
4. **Customize**: Configure features for your specific needs

The enhanced enterprise_kg_minimal provides a robust, scalable, and feature-rich alternative to enterprise_kg_graphiti, addressing its schema processing issues while adding powerful new capabilities.
