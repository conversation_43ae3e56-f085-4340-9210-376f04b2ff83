"""
Configuration Management for Enhanced Enterprise KG Minimal

This module provides configuration management for the enhanced system,
making it easy to set up and customize different deployment scenarios.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum


class DeploymentMode(Enum):
    """Deployment modes for different use cases."""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"
    MINIMAL = "minimal"


@dataclass
class Neo4jConfig:
    """Neo4j database configuration."""
    uri: str = "bolt://localhost:7687"
    user: str = "neo4j"
    password: str = "password"
    database: str = "neo4j"
    max_connection_lifetime: int = 3600
    max_connection_pool_size: int = 50


@dataclass
class LLMConfig:
    """LLM provider configuration."""
    provider: str = "openai"  # openai, anthropic, openrouter
    model: str = "gpt-4o"
    api_key: Optional[str] = None
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 60


@dataclass
class ChunkingConfig:
    """Document chunking configuration."""
    strategy: str = "hybrid"  # fixed_size, sentence_based, paragraph_based, semantic_based, hybrid
    chunk_size: int = 1000
    chunk_overlap: int = 200
    min_chunk_size: int = 100
    max_chunk_size: int = 2000
    preserve_sentences: bool = True
    preserve_paragraphs: bool = True


@dataclass
class HybridSearchConfig:
    """Hybrid search configuration."""
    enabled: bool = True
    default_method: str = "auto_hybrid"  # semantic_only, graph_only, template_hybrid, discovery_hybrid, auto_hybrid
    semantic_top_k: int = 10
    graph_top_k: int = 10
    final_top_k: int = 10
    pinecone_index_name: Optional[str] = None
    pinecone_api_key: Optional[str] = None


@dataclass
class EntityDiscoveryConfig:
    """Entity discovery configuration."""
    enabled: bool = True
    min_confidence: float = 0.3
    max_entities_per_chunk: int = 20
    enable_clustering: bool = True
    max_clusters: int = 10


@dataclass
class KnowledgeEnrichmentConfig:
    """Knowledge enrichment configuration."""
    enabled: bool = True
    max_enriched_queries: int = 5
    min_template_confidence: float = 0.3
    enable_intent_detection: bool = True


@dataclass
class ProcessingConfig:
    """Document processing configuration."""
    supported_file_types: list = None
    batch_size: int = 10
    max_file_size_mb: int = 100
    enable_parallel_processing: bool = True
    max_workers: int = 4


@dataclass
class EnhancedKGConfig:
    """Complete configuration for Enhanced Enterprise KG Minimal."""
    neo4j: Neo4jConfig
    llm: LLMConfig
    chunking: ChunkingConfig
    hybrid_search: HybridSearchConfig
    entity_discovery: EntityDiscoveryConfig
    knowledge_enrichment: KnowledgeEnrichmentConfig
    processing: ProcessingConfig
    
    def __post_init__(self):
        """Initialize default values after creation."""
        if self.processing.supported_file_types is None:
            self.processing.supported_file_types = ['.md', '.txt', '.docx', '.json', '.pdf']


class ConfigManager:
    """Manages configuration for different deployment scenarios."""
    
    @staticmethod
    def create_config(mode: DeploymentMode = DeploymentMode.DEVELOPMENT) -> EnhancedKGConfig:
        """
        Create configuration for a specific deployment mode.
        
        Args:
            mode: Deployment mode
            
        Returns:
            Complete configuration object
        """
        if mode == DeploymentMode.DEVELOPMENT:
            return ConfigManager._create_development_config()
        elif mode == DeploymentMode.PRODUCTION:
            return ConfigManager._create_production_config()
        elif mode == DeploymentMode.TESTING:
            return ConfigManager._create_testing_config()
        elif mode == DeploymentMode.MINIMAL:
            return ConfigManager._create_minimal_config()
        else:
            raise ValueError(f"Unknown deployment mode: {mode}")
    
    @staticmethod
    def _create_development_config() -> EnhancedKGConfig:
        """Create configuration optimized for development."""
        return EnhancedKGConfig(
            neo4j=Neo4jConfig(
                uri="bolt://localhost:7687",
                user="neo4j",
                password="password"
            ),
            llm=LLMConfig(
                provider="openai",
                model="gpt-4o",
                api_key=os.getenv("OPENAI_API_KEY"),
                temperature=0.1
            ),
            chunking=ChunkingConfig(
                strategy="hybrid",
                chunk_size=800,
                chunk_overlap=150
            ),
            hybrid_search=HybridSearchConfig(
                enabled=True,
                default_method="auto_hybrid"
            ),
            entity_discovery=EntityDiscoveryConfig(
                enabled=True,
                min_confidence=0.3
            ),
            knowledge_enrichment=KnowledgeEnrichmentConfig(
                enabled=True,
                max_enriched_queries=5
            ),
            processing=ProcessingConfig(
                batch_size=5,
                max_workers=2
            )
        )
    
    @staticmethod
    def _create_production_config() -> EnhancedKGConfig:
        """Create configuration optimized for production."""
        return EnhancedKGConfig(
            neo4j=Neo4jConfig(
                uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
                user=os.getenv("NEO4J_USER", "neo4j"),
                password=os.getenv("NEO4J_PASSWORD", "password"),
                max_connection_pool_size=100
            ),
            llm=LLMConfig(
                provider=os.getenv("LLM_PROVIDER", "openai"),
                model=os.getenv("LLM_MODEL", "gpt-4o"),
                api_key=os.getenv("LLM_API_KEY"),
                temperature=0.05,
                timeout=120
            ),
            chunking=ChunkingConfig(
                strategy="hybrid",
                chunk_size=1200,
                chunk_overlap=200
            ),
            hybrid_search=HybridSearchConfig(
                enabled=True,
                default_method="auto_hybrid",
                pinecone_index_name=os.getenv("PINECONE_INDEX_NAME"),
                pinecone_api_key=os.getenv("PINECONE_API_KEY")
            ),
            entity_discovery=EntityDiscoveryConfig(
                enabled=True,
                min_confidence=0.4,
                enable_clustering=True
            ),
            knowledge_enrichment=KnowledgeEnrichmentConfig(
                enabled=True,
                max_enriched_queries=10
            ),
            processing=ProcessingConfig(
                batch_size=20,
                max_workers=8,
                enable_parallel_processing=True
            )
        )
    
    @staticmethod
    def _create_testing_config() -> EnhancedKGConfig:
        """Create configuration optimized for testing."""
        return EnhancedKGConfig(
            neo4j=Neo4jConfig(
                uri="bolt://localhost:7687",
                user="neo4j",
                password="test_password",
                database="test_db"
            ),
            llm=LLMConfig(
                provider="openai",
                model="gpt-3.5-turbo",
                api_key=os.getenv("OPENAI_API_KEY"),
                temperature=0.0,
                max_tokens=2000
            ),
            chunking=ChunkingConfig(
                strategy="fixed_size",
                chunk_size=500,
                chunk_overlap=100
            ),
            hybrid_search=HybridSearchConfig(
                enabled=True,
                default_method="template_hybrid",
                semantic_top_k=5,
                graph_top_k=5
            ),
            entity_discovery=EntityDiscoveryConfig(
                enabled=True,
                min_confidence=0.2,
                max_entities_per_chunk=10
            ),
            knowledge_enrichment=KnowledgeEnrichmentConfig(
                enabled=True,
                max_enriched_queries=3
            ),
            processing=ProcessingConfig(
                batch_size=3,
                max_workers=1,
                max_file_size_mb=10
            )
        )
    
    @staticmethod
    def _create_minimal_config() -> EnhancedKGConfig:
        """Create minimal configuration with only essential features."""
        return EnhancedKGConfig(
            neo4j=Neo4jConfig(),
            llm=LLMConfig(
                provider="openai",
                model="gpt-3.5-turbo",
                api_key=os.getenv("OPENAI_API_KEY")
            ),
            chunking=ChunkingConfig(
                strategy="fixed_size",
                chunk_size=1000,
                chunk_overlap=100
            ),
            hybrid_search=HybridSearchConfig(
                enabled=False
            ),
            entity_discovery=EntityDiscoveryConfig(
                enabled=False
            ),
            knowledge_enrichment=KnowledgeEnrichmentConfig(
                enabled=False
            ),
            processing=ProcessingConfig(
                batch_size=5,
                max_workers=1,
                enable_parallel_processing=False
            )
        )
    
    @staticmethod
    def load_from_file(config_path: str) -> EnhancedKGConfig:
        """
        Load configuration from a JSON file.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Configuration object
        """
        import json
        
        with open(config_path, 'r') as f:
            config_dict = json.load(f)
        
        return ConfigManager._dict_to_config(config_dict)
    
    @staticmethod
    def save_to_file(config: EnhancedKGConfig, config_path: str):
        """
        Save configuration to a JSON file.
        
        Args:
            config: Configuration object
            config_path: Path to save configuration
        """
        import json
        
        config_dict = asdict(config)
        
        with open(config_path, 'w') as f:
            json.dump(config_dict, f, indent=2)
    
    @staticmethod
    def _dict_to_config(config_dict: Dict[str, Any]) -> EnhancedKGConfig:
        """Convert dictionary to configuration object."""
        return EnhancedKGConfig(
            neo4j=Neo4jConfig(**config_dict.get('neo4j', {})),
            llm=LLMConfig(**config_dict.get('llm', {})),
            chunking=ChunkingConfig(**config_dict.get('chunking', {})),
            hybrid_search=HybridSearchConfig(**config_dict.get('hybrid_search', {})),
            entity_discovery=EntityDiscoveryConfig(**config_dict.get('entity_discovery', {})),
            knowledge_enrichment=KnowledgeEnrichmentConfig(**config_dict.get('knowledge_enrichment', {})),
            processing=ProcessingConfig(**config_dict.get('processing', {}))
        )


def create_enhanced_processor_from_config(config: EnhancedKGConfig):
    """
    Create an enhanced processor from configuration.
    
    Args:
        config: Configuration object
        
    Returns:
        Configured EnhancedStandaloneProcessor
    """
    from enhanced_standalone_processor import create_enhanced_standalone_processor
    
    return create_enhanced_standalone_processor(
        neo4j_uri=config.neo4j.uri,
        neo4j_user=config.neo4j.user,
        neo4j_password=config.neo4j.password,
        llm_provider=config.llm.provider,
        llm_model=config.llm.model,
        llm_api_key=config.llm.api_key,
        chunking_strategy=config.chunking.strategy,
        chunk_size=config.chunking.chunk_size,
        chunk_overlap=config.chunking.chunk_overlap,
        enable_hybrid_search=config.hybrid_search.enabled,
        enable_entity_discovery=config.entity_discovery.enabled,
        enable_knowledge_enrichment=config.knowledge_enrichment.enabled
    )


# Example usage
if __name__ == "__main__":
    # Create development configuration
    dev_config = ConfigManager.create_config(DeploymentMode.DEVELOPMENT)
    
    # Save to file
    ConfigManager.save_to_file(dev_config, "config_dev.json")
    
    # Load from file
    loaded_config = ConfigManager.load_from_file("config_dev.json")
    
    # Create processor from config
    processor = create_enhanced_processor_from_config(loaded_config)
    
    print("✅ Enhanced processor created successfully!")
