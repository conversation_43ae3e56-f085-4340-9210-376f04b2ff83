"""
Enhanced Standalone Enterprise KG Processor

This module provides an enhanced version of the standalone processor that integrates
advanced chunking and hybrid search capabilities. It's designed to be more efficient
and feature-rich than the enterprise_kg_graphiti implementation.

Key Enhancements:
- Advanced chunking strategies
- Hybrid search integration
- Entity discovery and enrichment
- Knowledge graph optimization
- Performance improvements
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from dataclasses import asdict

# Import our enhanced components
from chunking_engine import ChunkingEngine, ChunkingStrategy, create_chunking_engine
from hybrid_search_engine import HybridSearchEngine, SearchMethod, create_hybrid_search_engine
from knowledge_enrichment import KnowledgeEnrichmentEngine, create_knowledge_enrichment_engine
from entity_discovery import EntityDiscoveryEngine, create_entity_discovery_engine

# Import existing components
from standalone_processor import StandaloneDocumentProcessor, LLMClient
from storage.neo4j_client import Neo4jClient, Neo4jConnection
from constants.schemas import DocumentSummary, EntityRelationship, ProcessingMetadata
from prompt_generator import PromptGenerator, create_full_prompt_generator


logger = logging.getLogger(__name__)


class EnhancedStandaloneProcessor:
    """
    Enhanced standalone processor with advanced chunking and hybrid search.
    
    This processor provides significant improvements over the basic standalone
    processor and the enterprise_kg_graphiti implementation:
    
    1. Advanced Chunking: Multiple strategies for optimal content segmentation
    2. Hybrid Search: Combines semantic and graph search capabilities
    3. Entity Discovery: Advanced entity extraction and relationship discovery
    4. Knowledge Enrichment: Template-based query enhancement
    5. Performance Optimization: Efficient processing and storage
    """
    
    def __init__(
        self,
        llm_client: LLMClient,
        neo4j_client: Neo4jClient,
        chunking_strategy: str = "hybrid",
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        enable_hybrid_search: bool = True,
        enable_entity_discovery: bool = True,
        enable_knowledge_enrichment: bool = True,
        semantic_search_client: Optional[Any] = None,
        prompt_generator: Optional[PromptGenerator] = None
    ):
        """
        Initialize the enhanced processor.
        
        Args:
            llm_client: LLM client for text processing
            neo4j_client: Neo4j client for graph storage
            chunking_strategy: Strategy for document chunking
            chunk_size: Target size for chunks
            chunk_overlap: Overlap between chunks
            enable_hybrid_search: Whether to enable hybrid search
            enable_entity_discovery: Whether to enable entity discovery
            enable_knowledge_enrichment: Whether to enable knowledge enrichment
            semantic_search_client: Optional semantic search client
            prompt_generator: Optional custom prompt generator
        """
        self.llm_client = llm_client
        self.neo4j_client = neo4j_client
        
        # Initialize chunking engine
        self.chunking_engine = create_chunking_engine(
            strategy=chunking_strategy,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        # Initialize hybrid search if enabled
        self.hybrid_search_engine = None
        if enable_hybrid_search:
            self.hybrid_search_engine = create_hybrid_search_engine(
                neo4j_uri=neo4j_client.connection.uri,
                neo4j_user=neo4j_client.connection.user,
                neo4j_password=neo4j_client.connection.password
            )
            if semantic_search_client:
                self.hybrid_search_engine.semantic_client = semantic_search_client
        
        # Initialize entity discovery if enabled
        self.entity_discovery_engine = None
        if enable_entity_discovery:
            self.entity_discovery_engine = create_entity_discovery_engine()
        
        # Initialize knowledge enrichment if enabled
        self.knowledge_enrichment_engine = None
        if enable_knowledge_enrichment:
            self.knowledge_enrichment_engine = create_knowledge_enrichment_engine()
        
        # Initialize prompt generator
        self.prompt_generator = prompt_generator or create_full_prompt_generator()
        
        # Configuration flags
        self.enable_hybrid_search = enable_hybrid_search
        self.enable_entity_discovery = enable_entity_discovery
        self.enable_knowledge_enrichment = enable_knowledge_enrichment
    
    def process_document(
        self, 
        file_path: str, 
        document_type: Optional[str] = None,
        enable_chunking: bool = True
    ) -> ProcessingMetadata:
        """
        Process a document with enhanced capabilities.
        
        Args:
            file_path: Path to the document
            document_type: Optional document type
            enable_chunking: Whether to use advanced chunking
            
        Returns:
            Processing metadata with results
        """
        logger.info(f"Enhanced processing of document: {file_path}")
        
        # Initialize metadata
        metadata = ProcessingMetadata(
            document_id=os.path.basename(file_path),
            document_path=file_path,
            file_size_bytes=0,
            processing_start_time=datetime.now()
        )
        
        try:
            # Read document content
            content = self._read_document(file_path)
            metadata.file_size_bytes = len(content.encode('utf-8'))
            
            if enable_chunking:
                # Use advanced chunking
                chunks = self.chunking_engine.chunk_document(content, file_path)
                logger.info(f"Created {len(chunks)} chunks using {self.chunking_engine.strategy.value} strategy")
                
                # Process each chunk
                all_relationships = []
                for chunk in chunks:
                    chunk_relationships = self._process_chunk(chunk.text, chunk.metadata.chunk_id)
                    all_relationships.extend(chunk_relationships)
                
                metadata.entity_extraction_completed = True
                metadata.relationship_extraction_completed = True
                logger.info(f"Extracted {len(all_relationships)} relationships from chunks")
            else:
                # Process entire document as one piece
                all_relationships = self._extract_relationships(content)
                metadata.entity_extraction_completed = True
                metadata.relationship_extraction_completed = True
                logger.info(f"Extracted {len(all_relationships)} relationships from full document")
            
            # Enhanced entity discovery if enabled
            if self.enable_entity_discovery and all_relationships:
                discovered_entities = self._discover_entities_from_relationships(all_relationships)
                logger.info(f"Discovered {len(discovered_entities)} entities")
            
            # Store in Neo4j
            if all_relationships:
                self._store_in_neo4j(all_relationships, metadata.document_id)
                metadata.graph_storage_completed = True
                logger.info("Stored relationships in Neo4j")
            
            metadata.processing_end_time = datetime.now()
            logger.info(f"Enhanced processing completed in {metadata.processing_duration_seconds:.2f}s")
            
        except Exception as e:
            logger.error(f"Enhanced processing failed for {file_path}: {e}")
            metadata.errors = [str(e)]
            metadata.processing_end_time = datetime.now()
        
        return metadata
    
    def search(
        self, 
        query: str, 
        method: str = "auto_hybrid",
        top_k: int = 10,
        org_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform enhanced search with hybrid capabilities.
        
        Args:
            query: Search query
            method: Search method to use
            top_k: Number of results to return
            org_id: Optional organization ID for filtering
            
        Returns:
            Enhanced search results
        """
        if not self.enable_hybrid_search or not self.hybrid_search_engine:
            logger.warning("Hybrid search not enabled, falling back to basic search")
            return self._basic_search(query, top_k)
        
        logger.info(f"Performing enhanced search: {query}")
        
        try:
            # Use hybrid search engine
            search_method = SearchMethod(method)
            response = self.hybrid_search_engine.search(
                query=query,
                method=search_method,
                top_k_semantic=top_k,
                top_k_graph=top_k,
                top_k_final=top_k,
                org_id=org_id
            )
            
            # Convert to dictionary for compatibility
            return {
                "query": response.query,
                "method": response.method,
                "answer": response.answer,
                "semantic_results": response.semantic_search,
                "graph_results": response.knowledge_graph,
                "confidence": response.confidence,
                "source_files": response.source_files,
                "processing_info": response.processing_info
            }
            
        except Exception as e:
            logger.error(f"Enhanced search failed: {e}")
            return {"error": str(e), "query": query}
    
    def enrich_query(self, query: str) -> List[Dict[str, Any]]:
        """
        Enrich a query with knowledge templates.
        
        Args:
            query: Natural language query
            
        Returns:
            List of enriched query options
        """
        if not self.enable_knowledge_enrichment or not self.knowledge_enrichment_engine:
            logger.warning("Knowledge enrichment not enabled")
            return []
        
        logger.info(f"Enriching query: {query}")
        
        try:
            enriched_queries = self.knowledge_enrichment_engine.enrich_query(query)
            
            # Convert to dictionaries
            return [
                {
                    "template": eq.template.value,
                    "cypher_query": eq.cypher_query,
                    "parameters": eq.parameters,
                    "confidence": eq.confidence,
                    "explanation": eq.explanation
                }
                for eq in enriched_queries
            ]
            
        except Exception as e:
            logger.error(f"Query enrichment failed: {e}")
            return []
    
    def discover_entities(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Discover entities from search results.
        
        Args:
            search_results: List of search results
            
        Returns:
            List of discovered entities
        """
        if not self.enable_entity_discovery or not self.entity_discovery_engine:
            logger.warning("Entity discovery not enabled")
            return []
        
        logger.info(f"Discovering entities from {len(search_results)} results")
        
        try:
            discovered_entities = self.entity_discovery_engine.discover_entities(search_results)
            
            # Convert to dictionaries
            return [
                {
                    "name": entity.name,
                    "type": entity.entity_type,
                    "confidence": entity.confidence,
                    "aliases": list(entity.aliases),
                    "properties": entity.properties,
                    "context_snippets": entity.context_snippets[:3]  # Limit for response size
                }
                for entity in discovered_entities
            ]
            
        except Exception as e:
            logger.error(f"Entity discovery failed: {e}")
            return []

    def _read_document(self, file_path: str) -> str:
        """Read document content based on file type."""
        file_extension = os.path.splitext(file_path)[1].lower()

        if file_extension == '.pdf':
            return self._read_pdf(file_path)
        elif file_extension == '.docx':
            return self._read_docx(file_path)
        elif file_extension == '.json':
            return self._read_json(file_path)
        else:
            # Default to text reading
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()

    def _read_pdf(self, file_path: str) -> str:
        """Extract text from PDF file."""
        try:
            import pypdf
            with open(file_path, 'rb') as f:
                pdf_reader = pypdf.PdfReader(f)
                text_content = []
                for page in pdf_reader.pages:
                    text_content.append(page.extract_text())
                return '\n'.join(text_content)
        except ImportError:
            logger.error("pypdf not installed. Install with: pip install pypdf")
            raise

    def _read_docx(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        try:
            from docx import Document
            doc = Document(file_path)
            text_content = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)

            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(' | '.join(row_text))

            return '\n'.join(text_content)
        except ImportError:
            logger.error("python-docx not installed. Install with: pip install python-docx")
            raise

    def _read_json(self, file_path: str) -> str:
        """Extract and format content from JSON file."""
        import json
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            return json.dumps(json_data, indent=2, ensure_ascii=False)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in file {file_path}: {e}")
            raise

    def _process_chunk(self, chunk_text: str, chunk_id: str) -> List[EntityRelationship]:
        """Process a single chunk and extract relationships."""
        try:
            # Use the prompt generator for relationship extraction
            prompt = self.prompt_generator.generate_relationship_extraction_prompt(chunk_text)
            schema_description = self.prompt_generator.get_schema_description()

            # Get LLM response
            response = self.llm_client.generate_structured_response(prompt, schema_description)

            if isinstance(response, list):
                relationships = []
                for rel_data in response:
                    try:
                        rel = EntityRelationship(**rel_data)
                        relationships.append(rel)
                    except Exception as e:
                        logger.warning(f"Failed to parse relationship from chunk {chunk_id}: {rel_data}, error: {e}")
                return relationships

        except Exception as e:
            logger.error(f"Failed to process chunk {chunk_id}: {e}")

        return []

    def _extract_relationships(self, content: str) -> List[EntityRelationship]:
        """Extract relationships from full document content."""
        try:
            prompt = self.prompt_generator.generate_relationship_extraction_prompt(content)
            schema_description = self.prompt_generator.get_schema_description()

            response = self.llm_client.generate_structured_response(prompt, schema_description)

            if isinstance(response, list):
                relationships = []
                for rel_data in response:
                    try:
                        rel = EntityRelationship(**rel_data)
                        relationships.append(rel)
                    except Exception as e:
                        logger.warning(f"Failed to parse relationship: {rel_data}, error: {e}")
                return relationships

        except Exception as e:
            logger.error(f"Failed to extract relationships: {e}")

        return []

    def _discover_entities_from_relationships(self, relationships: List[EntityRelationship]) -> List[Dict[str, Any]]:
        """Discover additional entities from extracted relationships."""
        if not self.entity_discovery_engine:
            return []

        # Convert relationships to search result format for entity discovery
        search_results = []
        for rel in relationships:
            result = {
                "metadata": {
                    "chunk_text": f"{rel.source_entity} {rel.relationship_type} {rel.target_entity}. {rel.context or ''}"
                }
            }
            search_results.append(result)

        # Discover entities
        discovered_entities = self.entity_discovery_engine.discover_entities(search_results)

        return [
            {
                "name": entity.name,
                "type": entity.entity_type,
                "confidence": entity.confidence,
                "properties": entity.properties
            }
            for entity in discovered_entities
        ]

    def _store_in_neo4j(self, relationships: List[EntityRelationship], source_document: str):
        """Store relationships in Neo4j with enhanced metadata."""
        try:
            results = self.neo4j_client.batch_create_entity_relationships(
                relationships, source_document
            )

            success_count = sum(1 for r in results if r["success"])
            logger.info(f"Successfully stored {success_count}/{len(results)} relationships")

            for result in results:
                if not result["success"]:
                    logger.warning(f"Failed to store relationship: {result['error']}")

        except Exception as e:
            logger.error(f"Failed to store relationships in Neo4j: {e}")
            raise

    def _basic_search(self, query: str, top_k: int) -> Dict[str, Any]:
        """Fallback basic search when hybrid search is not available."""
        try:
            # Simple Neo4j query for basic search
            cypher_query = """
                MATCH (e:Entity)-[r]->(t:Entity)
                WHERE e.name CONTAINS $query OR t.name CONTAINS $query
                RETURN e.name, type(r), t.name, r.fact
                LIMIT $limit
            """

            results = self.neo4j_client.execute_query(cypher_query, {"query": query, "limit": top_k})

            return {
                "query": query,
                "method": "basic_graph",
                "results": [dict(record) for record in results],
                "total_results": len(results)
            }

        except Exception as e:
            logger.error(f"Basic search failed: {e}")
            return {"error": str(e), "query": query}
