#!/usr/bin/env python3
"""
Quick Test Script for Enterprise KG Minimal

This script provides a quick way to test the minimal package
with sample data without requiring full Neo4j setup.
"""

import os
import sys
import tempfile
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_document():
    """Create a test document for processing."""
    content = """
# Project Alpha Status Report

## Overview
Project Alpha is a critical enterprise initiative focused on modernizing our customer relationship management system.

## Team Members
- **<PERSON>** (Project Manager) - Leading the overall project coordination
- **<PERSON>** (Lead Developer) - Responsible for backend development
- **<PERSON>** (UI/UX Designer) - Designing the user interface
- **<PERSON>** (Data Analyst) - Working on data migration strategies

## Project Details
John <PERSON> manages the Project Alpha team and reports to the Engineering Department. 
<PERSON> is involved in the CRM System development and works closely with the Database Team.
<PERSON> collaborates with the Design Department on user experience improvements.
<PERSON> analyzes customer data and works with the Data Science Team.

## Systems Integration
The new CRM System will integrate with:
- Customer Database
- Email Marketing Platform  
- Analytics Dashboard
- Mobile Application

## Next Steps
The team will continue development through Q4 2024, with <PERSON> coordinating weekly sprints.
<PERSON> will lead the technical architecture review next month.
"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
        f.write(content)
        return f.name

def test_extraction_only():
    """Test entity and relationship extraction without Neo4j storage."""
    print("🧪 Testing Entity/Relationship Extraction")
    print("=" * 50)
    
    try:
        from standalone_processor import LLMClient
        from prompt_generator import create_full_prompt_generator
        
        # Create test document
        test_file = create_test_document()
        print(f"✓ Created test document: {os.path.basename(test_file)}")
        
        # Read content
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✓ Document content: {len(content)} characters")
        
        # Create prompt generator
        prompt_gen = create_full_prompt_generator()
        print("✓ Prompt generator created")
        
        # Generate extraction prompt
        extraction_prompt = prompt_gen.generate_relationship_extraction_prompt(content)
        print(f"✓ Extraction prompt generated: {len(extraction_prompt)} characters")
        
        # Generate summarization prompt  
        summary_prompt = prompt_gen.generate_summarization_prompt(content)
        print(f"✓ Summarization prompt generated: {len(summary_prompt)} characters")
        
        # Show sample of what would be extracted
        print("\n📋 Sample Extraction Prompt (first 500 chars):")
        print("-" * 50)
        print(extraction_prompt[:500] + "...")
        
        print("\n📝 Expected Relationships:")
        print("-" * 50)
        expected_relationships = [
            "John Doe -> manages -> Project Alpha",
            "Sarah Smith -> involved_in -> CRM System",
            "Mike Johnson -> works_for -> Design Department", 
            "Lisa Chen -> works_with -> Data Science Team",
            "John Doe -> reports_to -> Engineering Department"
        ]
        
        for rel in expected_relationships:
            print(f"  • {rel}")
        
        # Clean up
        os.unlink(test_file)
        print(f"\n✓ Test completed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Extraction test failed: {e}")
        return False

def test_constants_usage():
    """Test that constants are properly loaded and used."""
    print("\n🔧 Testing Constants Usage")
    print("=" * 50)
    
    try:
        from constants.entities import get_all_entity_types, get_person_related_types
        from constants.relationships import get_all_relationship_types, get_project_relationships
        
        # Test entity types
        all_entities = get_all_entity_types()
        person_entities = get_person_related_types()
        
        print(f"✓ Total entity types: {len(all_entities)}")
        print(f"✓ Person-related entities: {len(person_entities)}")
        print(f"  Sample entities: {list(all_entities)[:5]}")
        
        # Test relationship types
        all_relationships = get_all_relationship_types()
        project_relationships = get_project_relationships()
        
        print(f"✓ Total relationship types: {len(all_relationships)}")
        print(f"✓ Project relationships: {len(project_relationships)}")
        print(f"  Sample relationships: {list(all_relationships)[:5]}")
        
        return True
        
    except Exception as e:
        print(f"✗ Constants test failed: {e}")
        return False

def test_schema_validation():
    """Test schema definitions."""
    print("\n📋 Testing Schema Validation")
    print("=" * 50)
    
    try:
        from constants.schemas import EntityRelationship, DocumentSummary, ProcessingMetadata
        from datetime import datetime
        
        # Test EntityRelationship
        rel = EntityRelationship(
            subject="John Doe",
            predicate="manages",
            object="Project Alpha",
            confidence_score=0.95,
            context="Project management",
            source_sentence="John Doe manages Project Alpha."
        )
        print(f"✓ EntityRelationship created: {rel.subject} {rel.predicate} {rel.object}")
        
        # Test DocumentSummary
        summary = DocumentSummary(
            title="Project Alpha Status",
            summary="Status report for Project Alpha initiative",
            document_type="report",
            key_topics=["project management", "team coordination"],
            language="en",
            confidence_score=0.9
        )
        print(f"✓ DocumentSummary created: {summary.title}")
        
        # Test ProcessingMetadata
        metadata = ProcessingMetadata(
            document_id="test_doc.md",
            document_path="/tmp/test_doc.md",
            file_size_bytes=1024,
            processing_start_time=datetime.now()
        )
        print(f"✓ ProcessingMetadata created: {metadata.document_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ Schema test failed: {e}")
        return False

def show_package_info():
    """Show information about the minimal package."""
    print("\n📦 Package Information")
    print("=" * 50)
    
    # Count files
    file_count = 0
    for root, dirs, files in os.walk("."):
        # Skip hidden directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        file_count += len([f for f in files if not f.startswith('.') and not f.endswith('.pyc')])
    
    print(f"✓ Total files in minimal package: {file_count}")
    
    # Show key features
    features = [
        "Standalone document processing",
        "Entity and relationship extraction", 
        "Neo4j knowledge graph storage",
        "OpenRouter/OpenAI/Anthropic LLM support",
        "Configurable entity/relationship types",
        "Sample document generation",
        "Environment-based configuration"
    ]
    
    print("✓ Key features:")
    for feature in features:
        print(f"  • {feature}")
    
    print("\n✓ Ready for:")
    print("  • Enterprise document processing")
    print("  • Knowledge graph construction") 
    print("  • Integration with existing systems")
    print("  • Production deployment")

def main():
    """Run quick tests for the minimal package."""
    print("🚀 Enterprise KG Minimal - Quick Test")
    print("=" * 60)
    
    tests = [
        ("Package Info", show_package_info),
        ("Constants Usage", test_constants_usage),
        ("Schema Validation", test_schema_validation),
        ("Extraction Test", test_extraction_only),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Quick Test Summary")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Minimal package is working correctly!")
        print("\n📋 Next Steps:")
        print("1. Set up Neo4j (local or Aura cloud)")
        print("2. Configure .env file with your credentials")
        print("3. Run: python test_config.py")
        print("4. Run: python main.py --create-samples")
        print("5. Explore your knowledge graph in Neo4j Browser")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
