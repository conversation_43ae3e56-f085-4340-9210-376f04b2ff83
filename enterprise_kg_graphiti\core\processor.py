"""
Main Graphiti Enterprise Processor

This module provides the main processor class that integrates Graphiti
with enterprise schema definitions for knowledge graph creation.
"""

import asyncio
import logging
import os # Added for path manipulation
import inspect # Added for debugging signature
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from graphiti_core import <PERSON><PERSON><PERSON><PERSON>
from graphiti_core.nodes import EpisodeType

from .schema_adapter import SchemaAdapter
from .document_processor import DocumentProcessor, ProcessedDocument, DocumentChunk
from .search_interface import SearchInterface
from .requesty_client import create_requesty_client
from .simple_embedder import create_simple_embedder

logger = logging.getLogger(__name__)

# Setup for chunk_details.log
CHUNK_DETAIL_LOG_DIR = Path(__file__).parent.parent / "logs"
CHUNK_DETAIL_LOG_FILE = CHUNK_DETAIL_LOG_DIR / "chunk_details.log"
os.makedirs(CHUNK_DETAIL_LOG_DIR, exist_ok=True)

chunk_detail_logger = logging.getLogger("ChunkDetails")
chunk_detail_logger.setLevel(logging.INFO)
file_handler = logging.FileHandler(CHUNK_DETAIL_LOG_FILE)
formatter = logging.Formatter('%(asctime)s - %(message)s')
file_handler.setFormatter(formatter)
chunk_detail_logger.addHandler(file_handler)
chunk_detail_logger.propagate = False # Prevent duplicate logs in root logger


class GraphitiEnterpriseProcessor:
    """
    Main processor for enterprise knowledge graph creation using Graphiti.

    This class integrates Graphiti's capabilities with enterprise schema
    definitions to provide a complete knowledge graph solution.
    """

    def __init__(self,
                 neo4j_uri: str,
                 neo4j_user: str,
                 neo4j_password: str,
                 llm_client=None,
                 embedder=None,
                 cross_encoder=None,
                 chunk_size: int = 1000,
                 chunk_overlap: int = 200,
                 store_raw_content: bool = True):
        """
        Initialize the Graphiti Enterprise Processor.

        Args:
            neo4j_uri: Neo4j database URI
            neo4j_user: Neo4j username
            neo4j_password: Neo4j password
            llm_client: Optional LLM client for Graphiti (defaults to Requesty)
            embedder: Optional embedder client for Graphiti (defaults to SimpleEmbedder)
            cross_encoder: Optional cross encoder client for Graphiti
            chunk_size: Size of document chunks in characters
            chunk_overlap: Overlap between chunks in characters
            store_raw_content: Whether to store raw episode content
        """
        # Use Requesty client if no LLM client provided
        if llm_client is None:
            llm_client = create_requesty_client()
            logger.info("Using Requesty API client for LLM")

        # Use simple embedder if no embedder provided
        if embedder is None:
            embedder = create_simple_embedder()
            logger.info("Using SimpleEmbedder for embeddings")

        # Initialize Graphiti
        self.graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            llm_client=llm_client,
            embedder=embedder,
            cross_encoder=cross_encoder,
            store_raw_episode_content=store_raw_content
        )

        # Initialize components
        self.schema_adapter = SchemaAdapter()
        self.document_processor = DocumentProcessor(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        self.search_interface = SearchInterface(self.graphiti, self.schema_adapter)

        # Processing settings
        self.group_id = "enterprise_kg"
        self.update_communities = True

        logger.info("GraphitiEnterpriseProcessor initialized successfully")

    async def initialize(self):
        """Initialize the knowledge graph database."""
        try:
            await self.graphiti.build_indices_and_constraints()
            logger.info("Knowledge graph indices and constraints built successfully")
        except Exception as e:
            logger.error(f"Failed to initialize knowledge graph: {e}")
            raise

    async def close(self):
        """Close the connection to the knowledge graph."""
        await self.graphiti.close()
        logger.info("Knowledge graph connection closed")

    async def process_file(self, file_path: str, document_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a single file and add it to the knowledge graph.

        Args:
            file_path: Path to the file to process
            document_type: Optional document type override

        Returns:
            Dictionary with processing results
        """
        try:
            # Process the document
            processed_doc = self.document_processor.process_file(file_path)
            if not processed_doc:
                return {
                    'success': False,
                    'error': f'Failed to process file: {file_path}',
                    'episodes_added': 0
                }

            # Override document type if provided
            if document_type:
                processed_doc.document_type = document_type

            # Add chunks as episodes to Graphiti
            episodes_added = 0
            for chunk in processed_doc.chunks:
                try:
                    result = await self._add_chunk_as_episode(chunk, processed_doc)
                    if result:
                        episodes_added += 1
                except Exception as e:
                    logger.error(f"Failed to add chunk {chunk.chunk_id}: {e}")

            return {
                'success': True,
                'document_id': processed_doc.document_id,
                'total_chunks': processed_doc.total_chunks,
                'episodes_added': episodes_added,
                'document_type': processed_doc.document_type
            }

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'episodes_added': 0
            }

    async def process_directory(self,
                              directory_path: str,
                              file_patterns: List[str] = None,
                              document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Process all files in a directory.

        Args:
            directory_path: Path to directory containing files
            file_patterns: List of file extensions to process
            document_type: Optional document type override for all files

        Returns:
            List of processing results for each file
        """
        try:
            # Process all documents
            processed_docs = self.document_processor.process_directory(
                directory_path, file_patterns
            )

            if not processed_docs:
                logger.warning(f"No documents found in {directory_path}")
                return []

            # Process each document
            results = []
            for processed_doc in processed_docs:
                try:
                    # Override document type if provided
                    if document_type:
                        processed_doc.document_type = document_type

                    # Add chunks as episodes
                    episodes_added = 0
                    logger.info(f"Processing {len(processed_doc.chunks)} chunks from {processed_doc.source_file}")

                    for chunk in processed_doc.chunks:
                        try:
                            # logger.debug(f"Processing chunk {chunk.chunk_index + 1}/{len(processed_doc.chunks)}") # Too verbose
                            result = await self._add_chunk_as_episode(chunk, processed_doc)
                            if result:
                                episodes_added += 1
                                # logger.debug(f"Successfully added chunk {chunk.chunk_index + 1}") # Too verbose
                            else:
                                logger.warning(f"Failed to add chunk {chunk.chunk_index + 1} from {processed_doc.source_file}")
                        except Exception as e:
                            logger.error(f"Failed to add chunk {chunk.chunk_id} from {processed_doc.source_file}: {e}")
                            # import traceback # Keep for critical errors if needed, but generally too verbose for release
                            # logger.debug(f"Chunk processing traceback: {traceback.format_exc()}") # Too verbose

                    results.append({
                        'success': True,
                        'file_path': processed_doc.source_file,
                        'document_id': processed_doc.document_id,
                        'total_chunks': processed_doc.total_chunks,
                        'episodes_added': episodes_added,
                        'document_type': processed_doc.document_type
                    })

                except Exception as e:
                    logger.error(f"Error processing document {processed_doc.source_file}: {e}")
                    results.append({
                        'success': False,
                        'file_path': processed_doc.source_file,
                        'error': str(e),
                        'episodes_added': 0
                    })

            return results

        except Exception as e:
            logger.error(f"Error processing directory {directory_path}: {e}")
            return []

    async def _add_chunk_as_episode(self,
                                   chunk: DocumentChunk,
                                   document: ProcessedDocument) -> bool:
        """
        Add a document chunk as an episode to Graphiti and create file source relationships.

        Args:
            chunk: Document chunk to add
            document: Parent document information

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create episode name
            episode_name = f"{Path(document.source_file).stem}_chunk_{chunk.chunk_index}"

            # Create source description
            source_description = (
                f"Document chunk from {document.source_file} "
                f"(type: {document.document_type}, chunk {chunk.chunk_index + 1}/{document.total_chunks})"
            )

            # logger.debug(f"Adding episode to Graphiti: {episode_name}") # Too verbose
            # logger.debug(f"Episode body length: {len(chunk.text)} characters") # Too verbose

            # Add episode to Graphiti (using default entity extraction for now)
            graphiti_entity_types = self.schema_adapter.get_entity_types_for_graphiti()
            graphiti_edge_types = self.schema_adapter.get_relationship_types_for_graphiti()
            graphiti_edge_type_map = self.schema_adapter.get_edge_type_map_for_graphiti()

            # Debug: Print the signature of the add_episode method being called
            try:
                logger.info(f"DEBUG: Graphiti object: {self.graphiti}")
                logger.info(f"DEBUG: Graphiti.add_episode signature: {inspect.signature(self.graphiti.add_episode)}")
            except Exception as e_inspect:
                logger.error(f"DEBUG: Error inspecting add_episode: {e_inspect}")


            episode_result = await self.graphiti.add_episode(
                name=episode_name,
                episode_body=chunk.text,
                source_description=source_description,
                reference_time=datetime.now(timezone.utc),
                source=EpisodeType.text,
                group_id=self.group_id,
                update_communities=self.update_communities,
                entity_types=None # Test without custom entity types
                # Temporarily removing edge_types and edge_type_map due to runtime signature mismatch
                # edge_types=graphiti_edge_types,
                # edge_type_map=graphiti_edge_type_map
            )

            # logger.debug(f"Episode added successfully, creating file relationships...") # Too verbose

            # Create file source node and relationships
            await self._create_file_source_relationships(document, episode_result)

            # Perform post-processing entity merging
            if episode_result.nodes:
                await self._merge_duplicate_entities(episode_result.nodes)

            # Log chunk details to the dedicated log file
            chunk_detail_logger.info(
                f"Chunk Processed:\n"
                f"  Source File: {document.source_file}\n"
                f"  Document ID: {document.document_id}\n"
                f"  Chunk Index: {chunk.chunk_index + 1}/{document.total_chunks}\n"
                f"  EpisodicNode UUID: {episode_result.episode.uuid}\n"
                f"  Chunk Text Length: {len(chunk.text)}\n"
                f"  Chunk Text (first 200 chars): {chunk.text[:200].replace(chr(10), ' ')}...\n"
                f"--------------------------------------------------"
            )

            # logger.debug(f"Added episode: {episode_name}") # Too verbose
            return True

        except Exception as e:
            logger.error(f"Failed to add chunk as episode for {document.source_file}, chunk_index {chunk.chunk_index}: {e}")
            # logger.error(f"Chunk details - Index: {chunk.chunk_index}, Text length: {len(chunk.text)}") # Covered by specific log if needed
            # logger.error(f"Document details - Source: {document.source_file}, Type: {document.document_type}") # Covered by specific log
            # logger.debug(f"Chunk text preview: {chunk.text[:200]}...") # Covered by specific log

            # Log the full exception traceback for debugging if critical, otherwise too verbose
            # import traceback
            # logger.debug(f"Full traceback: {traceback.format_exc()}")
            return False

    async def _create_file_source_relationships(self, document: ProcessedDocument, episode_result) -> None:
        """
        Create file source node and 'contains' relationships with extracted entities.

        Args:
            document: Document information
            episode_result: Result from adding episode to Graphiti
        """
        try:
            # Create file source node using Neo4j driver directly
            driver = self.graphiti.driver

            file_name = Path(document.source_file).name
            file_path = str(Path(document.source_file).absolute())

            # Create or update file source node
            async with driver.session() as session:
                # Create file source node
                file_query = """
                MERGE (f:FileSource {path: $file_path})
                SET f.name = $file_name,
                    f.document_type = $document_type,
                    f.total_chunks = $total_chunks,
                    f.document_id = $document_id,
                    f.created_at = datetime(),
                    f.updated_at = datetime()
                RETURN f
                """

                await session.run(file_query, {
                    'file_path': file_path,
                    'file_name': file_name,
                    'document_type': document.document_type,
                    'total_chunks': document.total_chunks,
                    'document_id': document.document_id
                })

                # Create 'contains' relationships with extracted entities
                if hasattr(episode_result, 'nodes') and episode_result.nodes:
                    for node in episode_result.nodes:
                        contains_query = """
                        MATCH (f:FileSource {path: $file_path})
                        MATCH (e:Entity {uuid: $entity_uuid})
                        MERGE (f)-[r:CONTAINS]->(e)
                        SET r.created_at = datetime(),
                            r.extraction_source = 'graphiti_enterprise'
                        """

                        await session.run(contains_query, {
                            'file_path': file_path,
                            'entity_uuid': node.uuid
                        })

                # Create relationship with episode
                episode_query = """
                MATCH (f:FileSource {path: $file_path})
                MATCH (ep:EpisodicNode {uuid: $episode_uuid})
                MERGE (f)-[r:CONTAINS]->(ep)
                SET r.created_at = datetime(),
                    r.relationship_type = 'file_contains_episode'
                """

                await session.run(episode_query, {
                    'file_path': file_path,
                    'episode_uuid': episode_result.episode.uuid
                })

            # logger.debug(f"Created file source relationships for {file_name}") # Too verbose

        except Exception as e:
            logger.error(f"Failed to create file source relationships for {document.source_file}: {e}")
            # Don't raise exception as this is supplementary functionality

    async def search(self,
                    query: str,
                    limit: int = 10,
                    entity_types: List[str] = None,
                    relationship_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        Search the knowledge graph.

        Args:
            query: Search query
            limit: Maximum number of results
            entity_types: Filter by entity types
            relationship_types: Filter by relationship types

        Returns:
            List of search results
        """
        return await self.search_interface.search(
            query=query,
            limit=limit,
            entity_types=entity_types,
            relationship_types=relationship_types
        )

    async def get_entity_relationships(self, entity_name: str) -> List[Dict[str, Any]]:
        """
        Get all relationships for a specific entity.

        Args:
            entity_name: Name of the entity

        Returns:
            List of relationships
        """
        return await self.search_interface.get_entity_relationships(entity_name)

    async def get_schema_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current schema.

        Returns:
            Dictionary with schema information
        """
        return {
            'entity_types': self.schema_adapter.get_supported_entity_types(),
            'relationship_types': self.schema_adapter.get_supported_relationship_types(),
            'total_entity_types': len(self.schema_adapter.get_supported_entity_types()),
            'total_relationship_types': len(self.schema_adapter.get_supported_relationship_types())
        }

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dictionary with processing statistics
        """
        return {
            'chunk_size': self.document_processor.chunk_size,
            'chunk_overlap': self.document_processor.chunk_overlap,
            'group_id': self.group_id,
            'update_communities': self.update_communities
        }

    async def _find_potential_duplicate_entities(self, entity_node) -> List[Dict[str, Any]]:
        """
        Find potential duplicate entities in Neo4j based on case-insensitive name and exact labels.
        Orders by creation date to help select the canonical one.
        """
        driver = self.graphiti.driver
        query = """
        MATCH (e:Entity)
        WHERE toLower(e.name) = toLower($name) AND e.labels = $labels
        RETURN e.uuid AS uuid, e.name AS name, e.labels AS labels, e.created_at AS created_at
        ORDER BY e.created_at ASC
        """
        async with driver.session() as session:
            result = await session.run(query, name=entity_node.name, labels=entity_node.labels)
            return [record.data() for record in await result.list()]

    async def _merge_entities_in_neo4j(self, canonical_node_uuid: str, duplicate_node_uuids: List[str]):
        """
        Merge duplicate entities into a canonical one using APOC (if available) or manual refactoring.
        This example assumes APOC. If not available, relationship refactoring needs to be done manually.
        """
        driver = self.graphiti.driver
        # Ensure we don't try to merge a node with itself if it's in the duplicate list by mistake
        safe_duplicate_node_uuids = [uuid for uuid in duplicate_node_uuids if uuid != canonical_node_uuid]
        if not safe_duplicate_node_uuids:
            return

        # Option 1: Using APOC (preferred if available)
        # Note: apoc.refactor.mergeNodes might have specific behaviors with properties;
        # ensure the desired properties (e.g., from canonical_node) are preserved.
        # The default strategy for conflicting properties might need to be specified.
        # For simplicity, this example assumes default behavior or that properties are already aligned.
        merge_query_apoc = """
        MATCH (canonical:Entity {uuid: $canonical_uuid})
        WITH canonical
        UNWIND $duplicate_uuids AS duplicate_uuid
        MATCH (duplicate:Entity {uuid: duplicate_uuid})
        WHERE canonical <> duplicate // Ensure not merging a node with itself
        CALL apoc.refactor.mergeNodes([canonical, duplicate], {properties: 'overwrite', mergeRels: true}) YIELD node
        RETURN count(node) as merged_count
        """
        # Simpler APOC version if property handling is straightforward:
        # merge_query_apoc = """
        # MATCH (canonical:Entity {uuid: $canonical_uuid})
        # WITH canonical
        # UNWIND $duplicate_uuids AS duplicate_uuid
        # MATCH (duplicate:Entity {uuid: duplicate_uuid})
        # WHERE canonical <> duplicate
        # CALL apoc.merge.node(['Entity'], {uuid: canonical.uuid}, duplicate) YIELD node // This might not work as expected for merging existing nodes
        # RETURN count(node)
        # """
        # A more robust APOC approach might involve apoc.refactor.to and apoc.refactor.from for relationships
        # then deleting the node. For now, using mergeNodes.

        # Option 2: Manual Merge (More complex, needed if APOC is not available)
        # This would involve:
        # 1. MATCH (d:Entity {uuid: $duplicate_uuid})-[r]->(t) CREATE (c:Entity {uuid: $canonical_uuid})-[new_r:TYPE(r)]->(t) SET new_r = properties(r)
        # 2. MATCH (s)-[r]->(d:Entity {uuid: $duplicate_uuid}) CREATE (s)-[new_r:TYPE(r)]->(c:Entity {uuid: $canonical_uuid}) SET new_r = properties(r)
        # 3. DETACH DELETE (d:Entity {uuid: $duplicate_uuid})
        # This needs to be done carefully for all relationship types and directions.

        async with driver.session() as session:
            try:
                # Attempt APOC merge
                logger.info(f"Attempting to merge duplicates {safe_duplicate_node_uuids} into {canonical_node_uuid} using APOC.")
                result = await session.run(merge_query_apoc, canonical_uuid=canonical_node_uuid, duplicate_uuids=safe_duplicate_node_uuids)
                summary = await result.consume()
                logger.info(f"APOC merge summary: {summary.counters}")
            except Exception as apoc_error:
                logger.warning(f"APOC merge failed: {apoc_error}. Manual merge would be required here if APOC is not installed or configured.")
                # Implement manual merge logic here if needed as a fallback.
                # For this example, we'll just log the warning.
                # Example of deleting duplicates after manual rel transfer (highly simplified):
                # delete_query_manual = """
                # UNWIND $duplicate_uuids AS duplicate_uuid
                # MATCH (d:Entity {uuid: duplicate_uuid})
                # DETACH DELETE d
                # """
                # await session.run(delete_query_manual, duplicate_uuids=safe_duplicate_node_uuids)
                # logger.info(f"Duplicates {safe_duplicate_node_uuids} would be deleted after manual relationship transfer.")
                pass


    async def _merge_duplicate_entities(self, resolved_entities: List[Any]):
        """
        Post-processing step to merge entities based on case-insensitive name and exact type.
        `resolved_entities` are the EntityNode objects returned by graphiti.add_episode.
        """
        if not resolved_entities:
            return

        for entity_obj in resolved_entities:
            # entity_obj is an instance of graphiti_core.nodes.EntityNode
            # We need its name and labels to find duplicates.
            # The labels list from graphiti-core already includes 'Entity' and the specific type.
            
            potential_duplicates = await self._find_potential_duplicate_entities(entity_obj)

            if len(potential_duplicates) > 1:
                logger.info(f"Found {len(potential_duplicates)} potential duplicates for entity name '{entity_obj.name}' (labels: {entity_obj.labels}): {potential_duplicates}")
                
                # The first one is the canonical one (oldest by created_at)
                canonical_node_data = potential_duplicates[0]
                duplicate_node_data_list = potential_duplicates[1:]
                
                duplicate_node_uuids = [data['uuid'] for data in duplicate_node_data_list]

                # Ensure the current entity_obj.uuid is among the duplicates if it's not the canonical one
                # This can happen if graphiti.add_episode returned a new node that is a duplicate of an existing one.
                if entity_obj.uuid != canonical_node_data['uuid'] and entity_obj.uuid not in duplicate_node_uuids:
                    # This case implies graphiti might have created a new node (entity_obj)
                    # that is a duplicate of an even older existing node (canonical_node_data).
                    # Or, entity_obj itself is one of the duplicates found.
                    # We need to ensure all non-canonical versions are merged.
                    # If entity_obj.uuid is not the canonical one, it should be in the list to be merged.
                     pass # The list of uuids from query should cover all versions.

                if duplicate_node_uuids:
                    logger.info(f"Merging entities: Canonical UUID: {canonical_node_data['uuid']}, Duplicates UUIDs: {duplicate_node_uuids}")
                    await self._merge_entities_in_neo4j(canonical_node_data['uuid'], duplicate_node_uuids)
                else:
                    logger.info(f"No further merging needed for '{entity_obj.name}' beyond what graphiti-core might have done.")
            # else:
                # logger.info(f"No duplicates found for entity '{entity_obj.name}' (labels: {entity_obj.labels}) requiring merge.")
