"""
Knowledge Enrichment Module for Enterprise KG Minimal

This module provides template-based query building and knowledge enrichment
capabilities for the hybrid search system. It includes predefined query patterns
for common enterprise use cases and intelligent query optimization.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import re

from constants.entities import EntityType
from constants.relationships import RelationshipType


logger = logging.getLogger(__name__)


class QueryTemplate(Enum):
    """Predefined query templates for common enterprise patterns."""
    PERSON_TO_PROJECT = "person_to_project"
    PROJECT_TO_PERSON = "project_to_person"
    PERSON_TO_SYSTEM = "person_to_system"
    SYSTEM_TO_PERSON = "system_to_person"
    ORGANIZATIONAL_HIERARCHY = "organizational_hierarchy"
    PROJECT_DEPENDENCIES = "project_dependencies"
    SYSTEM_INTEGRATIONS = "system_integrations"
    TEAM_COLLABORATION = "team_collaboration"
    RESOURCE_ALLOCATION = "resource_allocation"
    KNOWLEDGE_FLOW = "knowledge_flow"


@dataclass
class QueryContext:
    """Context information for query enrichment."""
    primary_entity: str
    secondary_entity: Optional[str] = None
    entity_types: List[str] = None
    relationship_types: List[str] = None
    depth_limit: int = 3
    result_limit: int = 20
    include_metadata: bool = True


@dataclass
class EnrichedQuery:
    """Enriched query with template and parameters."""
    template: QueryTemplate
    cypher_query: str
    parameters: Dict[str, Any]
    context: QueryContext
    confidence: float
    explanation: str


class KnowledgeEnrichmentEngine:
    """
    Engine for enriching queries with domain knowledge and templates.
    
    This class provides intelligent query building using predefined templates
    that capture common enterprise knowledge patterns and relationships.
    """
    
    def __init__(self):
        """Initialize the knowledge enrichment engine."""
        self.templates = self._initialize_templates()
        self.entity_patterns = self._initialize_entity_patterns()
        self.relationship_patterns = self._initialize_relationship_patterns()
    
    def enrich_query(
        self, 
        query_text: str, 
        context: Optional[QueryContext] = None
    ) -> List[EnrichedQuery]:
        """
        Enrich a natural language query with structured templates.
        
        Args:
            query_text: Natural language query
            context: Optional query context
            
        Returns:
            List of enriched queries ranked by confidence
        """
        logger.info(f"Enriching query: {query_text}")
        
        # Extract entities and relationships from query
        extracted_entities = self._extract_entities(query_text)
        extracted_relationships = self._extract_relationships(query_text)
        
        # Create or enhance context
        if not context:
            context = QueryContext(
                primary_entity=extracted_entities[0] if extracted_entities else "",
                secondary_entity=extracted_entities[1] if len(extracted_entities) > 1 else None,
                entity_types=self._infer_entity_types(extracted_entities),
                relationship_types=extracted_relationships
            )
        
        # Generate enriched queries for different templates
        enriched_queries = []
        
        for template in QueryTemplate:
            try:
                enriched_query = self._build_enriched_query(template, query_text, context)
                if enriched_query and enriched_query.confidence > 0.3:  # Minimum confidence threshold
                    enriched_queries.append(enriched_query)
            except Exception as e:
                logger.warning(f"Failed to build query for template {template}: {e}")
        
        # Sort by confidence
        enriched_queries.sort(key=lambda x: x.confidence, reverse=True)
        
        logger.info(f"Generated {len(enriched_queries)} enriched queries")
        return enriched_queries[:5]  # Return top 5
    
    def _initialize_templates(self) -> Dict[QueryTemplate, str]:
        """Initialize Cypher query templates."""
        return {
            QueryTemplate.PERSON_TO_PROJECT: """
                MATCH (person:Entity)-[r:INVOLVED_IN|MANAGES|OWNS|LEADS|RESPONSIBLE_FOR]->(project:Entity)
                WHERE person.name CONTAINS $primary_entity
                AND person.type IN $person_types
                AND project.type IN $project_types
                RETURN person.name as person, person.type as person_type,
                       type(r) as relationship, project.name as project, project.type as project_type,
                       r.confidence as confidence, r.fact as context
                ORDER BY r.confidence DESC
                LIMIT $limit
            """,
            
            QueryTemplate.PROJECT_TO_PERSON: """
                MATCH (project:Entity)<-[r:INVOLVED_IN|MANAGES|OWNS|LEADS|RESPONSIBLE_FOR]-(person:Entity)
                WHERE project.name CONTAINS $primary_entity
                AND project.type IN $project_types
                AND person.type IN $person_types
                RETURN project.name as project, project.type as project_type,
                       type(r) as relationship, person.name as person, person.type as person_type,
                       r.confidence as confidence, r.fact as context
                ORDER BY r.confidence DESC
                LIMIT $limit
            """,
            
            QueryTemplate.PERSON_TO_SYSTEM: """
                MATCH (person:Entity)-[r1:WORKS_FOR|INVOLVED_IN]->(dept:Entity)
                MATCH (dept)-[r2:USES|MANAGES|OWNS]->(system:Entity)
                WHERE person.name CONTAINS $primary_entity
                AND person.type IN $person_types
                AND system.type IN $system_types
                RETURN person.name as person, dept.name as department,
                       system.name as system, system.type as system_type,
                       type(r1) as person_dept_rel, type(r2) as dept_system_rel
                
                UNION
                
                MATCH (person:Entity)-[r:USES|INTEGRATES_WITH|MENTIONS]->(system:Entity)
                WHERE person.name CONTAINS $primary_entity
                AND person.type IN $person_types
                AND system.type IN $system_types
                RETURN person.name as person, null as department,
                       system.name as system, system.type as system_type,
                       type(r) as person_dept_rel, null as dept_system_rel
                LIMIT $limit
            """,
            
            QueryTemplate.ORGANIZATIONAL_HIERARCHY: """
                MATCH path = (top:Entity)-[:MANAGES|SUPERVISES|LEADS*1..3]->(bottom:Entity)
                WHERE (top.name CONTAINS $primary_entity OR bottom.name CONTAINS $primary_entity)
                AND top.type IN $person_types
                AND bottom.type IN $person_types
                RETURN top.name as manager, bottom.name as subordinate,
                       length(path) as hierarchy_level,
                       [r in relationships(path) | type(r)] as relationship_chain
                ORDER BY hierarchy_level
                LIMIT $limit
            """,
            
            QueryTemplate.SYSTEM_INTEGRATIONS: """
                MATCH (system1:Entity)-[r:INTEGRATES_WITH|CONNECTS_TO|DEPENDS_ON]->(system2:Entity)
                WHERE (system1.name CONTAINS $primary_entity OR system2.name CONTAINS $primary_entity)
                AND system1.type IN $system_types
                AND system2.type IN $system_types
                RETURN system1.name as source_system, system2.name as target_system,
                       type(r) as integration_type, r.fact as integration_details
                ORDER BY r.confidence DESC
                LIMIT $limit
            """,
            
            QueryTemplate.TEAM_COLLABORATION: """
                MATCH (person1:Entity)-[r1:COLLABORATES_WITH|WORKS_WITH]->(person2:Entity)
                WHERE (person1.name CONTAINS $primary_entity OR person2.name CONTAINS $primary_entity)
                AND person1.type IN $person_types
                AND person2.type IN $person_types
                
                OPTIONAL MATCH (person1)-[:INVOLVED_IN]->(project:Entity)<-[:INVOLVED_IN]-(person2)
                WHERE project.type IN $project_types
                
                RETURN person1.name as person1, person2.name as person2,
                       type(r1) as collaboration_type, project.name as shared_project
                LIMIT $limit
            """,
            
            QueryTemplate.KNOWLEDGE_FLOW: """
                MATCH path = (source:Entity)-[*1..3]->(target:Entity)
                WHERE source.name CONTAINS $primary_entity
                AND target.type IN $target_types
                AND ANY(r in relationships(path) WHERE type(r) IN ['MENTIONS', 'REFERENCES', 'DOCUMENTS', 'DESCRIBES'])
                RETURN source.name as knowledge_source, target.name as knowledge_target,
                       [r in relationships(path) | type(r)] as knowledge_path,
                       length(path) as path_length
                ORDER BY path_length
                LIMIT $limit
            """
        }
    
    def _initialize_entity_patterns(self) -> Dict[str, List[str]]:
        """Initialize entity recognition patterns."""
        return {
            "person": [
                r"\b[A-Z][a-z]+ [A-Z][a-z]+\b",  # First Last name
                r"\b(Mr|Ms|Dr|Prof)\.? [A-Z][a-z]+ [A-Z][a-z]+\b",  # Title + name
                r"\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:manages|leads|works|develops)\b"
            ],
            "project": [
                r"\bProject [A-Z][a-zA-Z0-9\s]+\b",
                r"\b[A-Z][a-zA-Z0-9\s]+ (?:Project|Initiative|Program)\b",
                r"\b(?:project|initiative|program) (?:called|named) [A-Z][a-zA-Z0-9\s]+\b"
            ],
            "system": [
                r"\b[A-Z][a-zA-Z0-9\s]+ (?:System|Platform|Application|Tool|Database)\b",
                r"\b(?:CRM|ERP|API|UI|UX|ML|AI) [A-Z][a-zA-Z0-9\s]*\b",
                r"\b[A-Z][a-zA-Z0-9]+ (?:integrates|connects|interfaces)\b"
            ],
            "department": [
                r"\b[A-Z][a-zA-Z\s]+ (?:Department|Division|Team|Group)\b",
                r"\b(?:Engineering|Marketing|Sales|HR|Finance|IT|Design) (?:Department|Team|Division)?\b"
            ]
        }
    
    def _initialize_relationship_patterns(self) -> Dict[str, List[str]]:
        """Initialize relationship recognition patterns."""
        return {
            "manages": [r"\bmanages?\b", r"\bleads?\b", r"\bsupervises?\b", r"\boversees?\b"],
            "works_for": [r"\bworks? for\b", r"\bemployed by\b", r"\breports? to\b"],
            "involved_in": [r"\binvolved in\b", r"\bparticipates? in\b", r"\bworks? on\b"],
            "uses": [r"\buses?\b", r"\butilizes?\b", r"\boperates?\b", r"\baccesses?\b"],
            "integrates_with": [r"\bintegrates? with\b", r"\bconnects? to\b", r"\binterfaces? with\b"],
            "collaborates_with": [r"\bcollaborates? with\b", r"\bworks? with\b", r"\bpartners? with\b"]
        }
    
    def _extract_entities(self, query_text: str) -> List[str]:
        """Extract entity names from query text."""
        entities = []
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, query_text, re.IGNORECASE)
                entities.extend(matches)
        
        # Also extract quoted strings and capitalized words
        quoted_entities = re.findall(r'"([^"]*)"', query_text)
        entities.extend(quoted_entities)
        
        # Extract capitalized words (potential proper nouns)
        words = query_text.split()
        for word in words:
            clean_word = re.sub(r'[^\w\s]', '', word)
            if (clean_word and clean_word[0].isupper() and len(clean_word) > 2 and
                clean_word.lower() not in ['the', 'and', 'for', 'with', 'this', 'that', 'what', 'who', 'how']):
                entities.append(clean_word)
        
        # Remove duplicates while preserving order
        unique_entities = []
        for entity in entities:
            if entity not in unique_entities:
                unique_entities.append(entity)
        
        return unique_entities[:5]  # Limit to top 5
    
    def _extract_relationships(self, query_text: str) -> List[str]:
        """Extract relationship types from query text."""
        relationships = []
        
        for rel_type, patterns in self.relationship_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_text, re.IGNORECASE):
                    relationships.append(rel_type.upper())
                    break
        
        return relationships

    def _infer_entity_types(self, entities: List[str]) -> List[str]:
        """Infer entity types from entity names."""
        types = set()

        for entity in entities:
            entity_lower = entity.lower()

            # Person indicators
            if any(indicator in entity_lower for indicator in ['john', 'jane', 'smith', 'johnson', 'manager', 'developer', 'analyst']):
                types.update(['Person', 'Employee', 'Manager'])

            # Project indicators
            if any(indicator in entity_lower for indicator in ['project', 'initiative', 'program', 'alpha', 'beta']):
                types.update(['Project', 'Initiative', 'Program'])

            # System indicators
            if any(indicator in entity_lower for indicator in ['system', 'platform', 'application', 'crm', 'erp', 'api', 'database']):
                types.update(['System', 'Application', 'Platform', 'Database'])

            # Department indicators
            if any(indicator in entity_lower for indicator in ['department', 'team', 'division', 'engineering', 'marketing', 'sales']):
                types.update(['Department', 'Team', 'Organization'])

        # Default types if none detected
        if not types:
            types.update(['Entity', 'Person', 'Project', 'System'])

        return list(types)

    def _build_enriched_query(
        self,
        template: QueryTemplate,
        query_text: str,
        context: QueryContext
    ) -> Optional[EnrichedQuery]:
        """Build an enriched query for a specific template."""
        if template not in self.templates:
            return None

        cypher_template = self.templates[template]

        # Build parameters based on template and context
        parameters = self._build_template_parameters(template, context)

        # Calculate confidence based on query-template match
        confidence = self._calculate_template_confidence(template, query_text, context)

        # Generate explanation
        explanation = self._generate_explanation(template, context)

        return EnrichedQuery(
            template=template,
            cypher_query=cypher_template,
            parameters=parameters,
            context=context,
            confidence=confidence,
            explanation=explanation
        )

    def _build_template_parameters(self, template: QueryTemplate, context: QueryContext) -> Dict[str, Any]:
        """Build parameters for a specific template."""
        params = {
            "primary_entity": context.primary_entity,
            "limit": context.result_limit
        }

        if context.secondary_entity:
            params["secondary_entity"] = context.secondary_entity

        # Add type-specific parameters
        if template in [QueryTemplate.PERSON_TO_PROJECT, QueryTemplate.PROJECT_TO_PERSON,
                       QueryTemplate.ORGANIZATIONAL_HIERARCHY, QueryTemplate.TEAM_COLLABORATION]:
            params.update({
                "person_types": ["Person", "Employee", "Manager", "Developer", "Analyst"],
                "project_types": ["Project", "Initiative", "Program", "Task"]
            })

        elif template in [QueryTemplate.PERSON_TO_SYSTEM, QueryTemplate.SYSTEM_INTEGRATIONS]:
            params.update({
                "person_types": ["Person", "Employee", "Manager"],
                "system_types": ["System", "Application", "Platform", "Database", "Tool"]
            })

        elif template == QueryTemplate.KNOWLEDGE_FLOW:
            params.update({
                "target_types": context.entity_types or ["Document", "Knowledge", "Information"]
            })

        return params

    def _calculate_template_confidence(
        self,
        template: QueryTemplate,
        query_text: str,
        context: QueryContext
    ) -> float:
        """Calculate confidence score for template match."""
        confidence = 0.0
        query_lower = query_text.lower()

        # Base confidence on template-specific keywords
        template_keywords = {
            QueryTemplate.PERSON_TO_PROJECT: ["who", "works", "project", "involved", "team"],
            QueryTemplate.PROJECT_TO_PERSON: ["project", "team", "members", "assigned"],
            QueryTemplate.PERSON_TO_SYSTEM: ["system", "uses", "tools", "platform"],
            QueryTemplate.ORGANIZATIONAL_HIERARCHY: ["manager", "reports", "hierarchy", "supervises"],
            QueryTemplate.SYSTEM_INTEGRATIONS: ["integrates", "connects", "system", "interface"],
            QueryTemplate.TEAM_COLLABORATION: ["collaborates", "works with", "team", "together"],
            QueryTemplate.KNOWLEDGE_FLOW: ["documents", "knowledge", "information", "references"]
        }

        keywords = template_keywords.get(template, [])
        keyword_matches = sum(1 for keyword in keywords if keyword in query_lower)
        confidence += (keyword_matches / len(keywords)) * 0.6

        # Boost confidence if entities are present
        if context.primary_entity:
            confidence += 0.2
        if context.secondary_entity:
            confidence += 0.1

        # Boost confidence if relationship types match
        if context.relationship_types:
            confidence += 0.1

        return min(1.0, confidence)

    def _generate_explanation(self, template: QueryTemplate, context: QueryContext) -> str:
        """Generate human-readable explanation for the enriched query."""
        explanations = {
            QueryTemplate.PERSON_TO_PROJECT: f"Finding projects that {context.primary_entity} is involved in",
            QueryTemplate.PROJECT_TO_PERSON: f"Finding people working on {context.primary_entity}",
            QueryTemplate.PERSON_TO_SYSTEM: f"Finding systems that {context.primary_entity} uses or has access to",
            QueryTemplate.ORGANIZATIONAL_HIERARCHY: f"Finding organizational relationships involving {context.primary_entity}",
            QueryTemplate.SYSTEM_INTEGRATIONS: f"Finding system integrations related to {context.primary_entity}",
            QueryTemplate.TEAM_COLLABORATION: f"Finding collaboration relationships involving {context.primary_entity}",
            QueryTemplate.KNOWLEDGE_FLOW: f"Finding knowledge and information flow from {context.primary_entity}"
        }

        return explanations.get(template, f"Analyzing {template.value} patterns for {context.primary_entity}")


def create_knowledge_enrichment_engine() -> KnowledgeEnrichmentEngine:
    """Create a knowledge enrichment engine with default configuration."""
    return KnowledgeEnrichmentEngine()
