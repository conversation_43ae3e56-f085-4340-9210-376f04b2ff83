# Quick Start Guide - Enterprise Knowledge Graph with Graphiti

This guide will help you get started with the Enterprise Knowledge Graph system using Graphiti and your existing schema definitions.

## Prerequisites

1. **Neo4j Database** - Running and accessible
2. **Requesty API Key** - For LLM functionality
3. **Python 3.8+** - With required dependencies

## Step 1: Environment Setup

1. **Copy the environment template:**
   ```bash
   cp .env.template .env
   ```

2. **Edit `.env` with your credentials:**
   ```env
   # Neo4j Configuration
   NEO4J_URI=bolt://localhost:7687
   NEO4J_USER=neo4j
   NEO4J_PASSWORD=your_actual_password

   # Requesty API Configuration
   REQUESTY_API_KEY=your_actual_requesty_api_key
   REQUESTY_BASE_URL=https://router.requesty.ai/v1

   # LLM Configuration
   LLM_MODEL=anthropic/claude-3.5-sonnet
   ```

## Step 2: Install Dependencies

```bash
pip install -r requirements.txt
```

## Step 3: Test Your Setup

Run the setup test to verify everything is working:

```bash
python test_setup.py
```

This will test:
- ✅ Configuration validation
- ✅ Requesty API connection
- ✅ Simple embedder functionality
- ✅ Document processor
- ✅ Schema adapter
- ✅ Neo4j connection

## Step 4: Process Your Documents

### Option A: Use the documents folder (recommended)

The system will automatically process documents from the `./documents` folder:

```bash
python main.py
```

### Option B: Process specific directory

```bash
python main.py --documents /path/to/your/documents
```

### Option C: Process single file

```bash
python main.py --file /path/to/document.pdf
```

## Step 5: Run End-to-End Flow

For a complete demonstration including Cypher queries and hybrid search:

```bash
python end_to_end_flow.py
```

This will:
1. Process documents
2. Create file source relationships
3. Demonstrate Cypher queries
4. Show hybrid search capabilities
5. Analyze entity relationships

## Step 6: Explore Your Knowledge Graph

### Neo4j Browser

1. Open Neo4j Browser: http://localhost:7474
2. Connect with your credentials
3. Run queries to explore the graph:

```cypher
// See all file sources
MATCH (f:FileSource) RETURN f

// See file sources and their content
MATCH (f:FileSource)-[r:CONTAINS]->(content)
RETURN f.name, type(r), labels(content), count(content)

// See extracted entities
MATCH (f:FileSource)-[:CONTAINS]->(e:Entity)
RETURN f.name as source, e.name as entity, labels(e) as types

// See entity relationships
MATCH (e1:Entity)-[r]->(e2:Entity)
RETURN e1.name, type(r), e2.name, r.fact
```

### Programmatic Access

```python
import asyncio
from core.processor import GraphitiEnterpriseProcessor

async def search_example():
    processor = GraphitiEnterpriseProcessor(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="your_password"
    )
    
    await processor.initialize()
    
    # Hybrid search
    results = await processor.search("company culture", limit=10)
    for result in results:
        print(result.get('fact', result.get('name')))
    
    await processor.close()

asyncio.run(search_example())
```

## Key Features

### 1. File Source Tracking
- Every processed document creates a `FileSource` node
- `CONTAINS` relationships link files to extracted entities
- Full traceability from entities back to source documents

### 2. Schema Compliance
- Uses your existing entity and relationship types
- Validates extracted entities against enterprise ontology
- Maintains consistency with your schema definitions

### 3. Hybrid Search
- Combines semantic similarity with keyword matching
- Schema-aware filtering by entity/relationship types
- Graph-based result ranking

### 4. Requesty API Integration
- Uses your Requesty API for LLM functionality
- No dependency on OpenAI or other external APIs
- Configurable model selection

### 5. Simple Embedder
- No external API required for embeddings
- Hash-based consistent embeddings
- Suitable for basic similarity matching

## Common Commands

```bash
# Process documents with custom chunk size
python main.py --chunk-size 1500 --chunk-overlap 300

# Process only specific file types
python main.py --extensions .pdf .docx

# Search after processing
python main.py --search "team collaboration"

# Check configuration
python main.py --config-check

# Show schema information
python main.py --schema-info

# Create sample documents for testing
python main.py --create-samples
```

## Troubleshooting

### Neo4j Connection Issues
```bash
# Check if Neo4j is running
neo4j status

# Start Neo4j if needed
neo4j start
```

### Requesty API Issues
- Verify your API key is correct
- Check your account quota/billing
- Ensure the base URL is correct

### Document Processing Issues
- Check file permissions
- Verify supported file formats (.pdf, .docx, .txt, .json, .md)
- Review chunk size settings if memory issues occur

### Schema Issues
- Ensure `enterprise_kg_minimal/constants/` is accessible
- Check that entity and relationship types are properly defined

## Next Steps

1. **Explore the Graph**: Use Neo4j Browser to visualize your knowledge graph
2. **Custom Queries**: Write Cypher queries for specific business insights
3. **Integration**: Use the processor API in your applications
4. **Expansion**: Add more documents to grow your knowledge graph
5. **Customization**: Modify schema definitions for your specific needs

## Support

- Check the main README.md for detailed documentation
- Run `python test_setup.py` to diagnose issues
- Review logs for detailed error information
- Ensure all environment variables are properly set
