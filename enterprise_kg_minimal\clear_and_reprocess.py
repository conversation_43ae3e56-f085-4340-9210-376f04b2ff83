#!/usr/bin/env python3
"""
Clear existing Neo4j data and reprocess documents with enhanced properties.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from storage.neo4j_client import Neo4jClient, Neo4jConnection
from standalone_processor import create_standalone_processor


def clear_neo4j_database():
    """Clear all data from Neo4j database."""
    print("🗑️  Clearing Neo4j database...")

    connection = Neo4jConnection(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        user=os.getenv("NEO4J_USER", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
        database=os.getenv("NEO4J_DATABASE")
    )
    client = Neo4jClient(connection)

    try:
        driver = client._get_driver()
        with driver.session(database=client.connection.database) as session:
            # Delete all relationships first
            print("   Deleting all relationships...")
            result = session.run("MATCH ()-[r]-() DELETE r")
            print(f"   ✓ Relationships deleted")

            # Delete all nodes
            print("   Deleting all nodes...")
            result = session.run("MATCH (n) DELETE n")
            print(f"   ✓ Nodes deleted")

            # Verify database is empty
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()["node_count"]

            result = session.run("MATCH ()-[r]-() RETURN count(r) as rel_count")
            rel_count = result.single()["rel_count"]

            print(f"   ✓ Database cleared: {node_count} nodes, {rel_count} relationships remaining")

    except Exception as e:
        print(f"   ❌ Error clearing database: {e}")
        return False
    finally:
        client.close()

    return True


def reprocess_documents():
    """Reprocess documents with enhanced properties."""
    print("\n📄 Reprocessing documents with enhanced properties...")

    try:
        # Create processor
        processor = create_standalone_processor(
            neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            neo4j_user=os.getenv("NEO4J_USER", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
            llm_provider=os.getenv("LLM_PROVIDER", "openrouter"),
            llm_model=os.getenv("LLM_MODEL", "anthropic/claude-3.5-sonnet")
        )

        # Process documents directory
        documents_dir = "documents"
        if not os.path.exists(documents_dir):
            print(f"   ❌ Documents directory not found: {documents_dir}")
            return False

        print(f"   Processing documents from: {documents_dir}")
        results = processor.process_directory(
            documents_dir,
            file_patterns=[".md", ".txt", ".docx", ".pdf"]
        )

        # Print results
        successful = sum(1 for r in results if r.graph_storage_completed)
        total = len(results)

        print(f"   ✓ Processed {successful}/{total} documents successfully")

        for result in results:
            if result.graph_storage_completed:
                print(f"     ✓ {result.document_id} - {result.processing_duration_seconds:.1f}s")
            else:
                print(f"     ❌ {result.document_id} - {result.errors or 'Processing incomplete'}")

        processor.neo4j_client.close()
        return successful > 0

    except Exception as e:
        print(f"   ❌ Error processing documents: {e}")
        return False


def show_enhanced_graph_stats():
    """Show statistics of the enhanced graph."""
    print("\n📊 Enhanced Graph Statistics...")

    connection = Neo4jConnection(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        user=os.getenv("NEO4J_USER", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
        database=os.getenv("NEO4J_DATABASE")
    )
    client = Neo4jClient(connection)

    try:
        driver = client._get_driver()
        with driver.session(database=client.connection.database) as session:
            # Total counts
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()["node_count"]

            result = session.run("MATCH ()-[r]-() RETURN count(r) as rel_count")
            rel_count = result.single()["rel_count"]

            print(f"   📈 Total: {node_count} nodes, {rel_count} relationships")

            # Nodes by category
            print("\n   📋 Nodes by Category:")
            result = session.run("""
                MATCH (n)
                WHERE n.category IS NOT NULL
                RETURN n.category as category, count(n) as count
                ORDER BY count DESC
            """)
            for record in result:
                print(f"     • {record['category']}: {record['count']} nodes")

            # High importance entities
            print("\n   ⭐ High-Importance Entities (>0.8):")
            result = session.run("""
                MATCH (n)
                WHERE n.graph_importance > 0.8
                RETURN n.name, n.entity_type, n.graph_importance
                ORDER BY n.graph_importance DESC
                LIMIT 10
            """)
            for record in result:
                print(f"     • {record['n.name']} ({record['n.entity_type']}) - {record['n.graph_importance']:.2f}")

            # Leadership entities
            print("\n   👑 Leadership Entities:")
            result = session.run("""
                MATCH (n)
                WHERE n.leadership_role = true
                RETURN n.name, n.entity_type
                LIMIT 5
            """)
            for record in result:
                print(f"     • {record['n.name']} ({record['n.entity_type']})")

            # Technology entities
            print("\n   💻 Technology Entities:")
            result = session.run("""
                MATCH (n)
                WHERE n.is_technology = true
                RETURN n.name, n.entity_type
                LIMIT 5
            """)
            for record in result:
                print(f"     • {record['n.name']} ({record['n.entity_type']})")

            # Sample enhanced properties
            print("\n   🔍 Sample Enhanced Properties:")
            result = session.run("""
                MATCH (n)
                WHERE n.graph_importance IS NOT NULL
                RETURN n.name, n.entity_type, n.description, n.category, n.graph_importance
                LIMIT 3
            """)
            for record in result:
                print(f"     • {record['n.name']} ({record['n.entity_type']})")
                print(f"       Description: {record['n.description']}")
                print(f"       Category: {record['n.category']}")
                print(f"       Importance: {record['n.graph_importance']:.2f}")
                print()

    except Exception as e:
        print(f"   ❌ Error getting graph stats: {e}")
    finally:
        client.close()


def show_sample_queries():
    """Show sample queries to explore the enhanced graph."""
    print("\n🔍 Sample Queries to Explore Enhanced Graph:")
    print("=" * 50)

    queries = [
        {
            "name": "View All Enhanced Properties",
            "query": "MATCH (n) RETURN n.name, n.entity_type, n.category, n.graph_importance, n.description LIMIT 10"
        },
        {
            "name": "Find High-Importance Entities",
            "query": "MATCH (n) WHERE n.graph_importance > 0.8 RETURN n.name, n.entity_type, n.graph_importance ORDER BY n.graph_importance DESC"
        },
        {
            "name": "Find Leadership Network",
            "query": "MATCH (leader) WHERE leader.leadership_role = true OPTIONAL MATCH (leader)-[r]-(other) RETURN leader.name, type(r), other.name"
        },
        {
            "name": "Technology Integration Map",
            "query": "MATCH (tech) WHERE tech.is_technology = true OPTIONAL MATCH (tech)-[r]-(other) RETURN tech.name, type(r), other.name"
        },
        {
            "name": "Knowledge Sources",
            "query": "MATCH (doc) WHERE doc.contains_knowledge = true RETURN doc.name, doc.category, doc.description"
        },
        {
            "name": "Entities by Category",
            "query": "MATCH (n) WHERE n.category IS NOT NULL RETURN n.category, collect(n.name) as entities ORDER BY n.category"
        }
    ]

    for i, query_info in enumerate(queries, 1):
        print(f"\n{i}. {query_info['name']}:")
        print(f"   {query_info['query']}")


def main():
    """Main function to clear and reprocess."""
    print("🚀 Clear Neo4j and Reprocess with Enhanced Properties")
    print("=" * 60)

    # Step 1: Clear existing data
    if not clear_neo4j_database():
        print("❌ Failed to clear database. Exiting.")
        return 1

    # Step 2: Reprocess documents
    if not reprocess_documents():
        print("❌ Failed to reprocess documents. Exiting.")
        return 1

    # Step 3: Show enhanced graph statistics
    show_enhanced_graph_stats()

    # Step 4: Show sample queries
    show_sample_queries()

    print("\n✅ Database cleared and reprocessed with enhanced properties!")
    print("💡 Use Neo4j Browser to explore the enhanced graph:")
    print(f"   URI: {os.getenv('NEO4J_URI', 'bolt://localhost:7687')}")
    print("   Try the sample queries above to see the enhanced properties in action!")

    return 0


if __name__ == "__main__":
    sys.exit(main())
