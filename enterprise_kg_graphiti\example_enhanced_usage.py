"""
Example Usage of Enhanced Enterprise KG Minimal

This example demonstrates how to use the enhanced enterprise_kg_minimal system
with advanced chunking, hybrid search, entity discovery, and knowledge enrichment.

The enhanced system provides significant improvements over enterprise_kg_graphiti:
1. Better chunking strategies for optimal knowledge extraction
2. Hybrid search combining semantic and graph approaches
3. Advanced entity discovery and relationship mapping
4. Template-based query enrichment
5. Performance optimizations
"""

import asyncio
import logging
from typing import List, Dict, Any

from enhanced_standalone_processor import create_enhanced_standalone_processor
from chunking_engine import ChunkingStrategy
from hybrid_search_engine import SearchMethod

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def basic_usage_example():
    """Basic usage example of the enhanced processor."""
    print("\n" + "="*60)
    print("BASIC USAGE EXAMPLE")
    print("="*60)
    
    # Create enhanced processor with all features enabled
    processor = create_enhanced_standalone_processor(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="password",
        llm_provider="openai",  # or "anthropic", "openrouter"
        llm_model="gpt-4o",
        chunking_strategy="hybrid",
        chunk_size=1000,
        chunk_overlap=200,
        enable_hybrid_search=True,
        enable_entity_discovery=True,
        enable_knowledge_enrichment=True
    )
    
    # Process a single document with advanced chunking
    print("\n1. Processing document with advanced chunking...")
    metadata = processor.process_document(
        file_path="documents/sample_document.md",
        enable_chunking=True
    )
    
    print(f"   ✓ Processed: {metadata.document_id}")
    print(f"   ✓ File size: {metadata.file_size_bytes} bytes")
    print(f"   ✓ Processing time: {metadata.processing_duration_seconds:.2f}s")
    print(f"   ✓ Entity extraction: {'✓' if metadata.entity_extraction_completed else '✗'}")
    print(f"   ✓ Graph storage: {'✓' if metadata.graph_storage_completed else '✗'}")
    
    # Perform hybrid search
    print("\n2. Performing hybrid search...")
    search_results = await processor.search(
        query="Who is working on Project Alpha?",
        method="auto_hybrid",
        top_k=10
    )
    
    print(f"   ✓ Query: {search_results.get('query', 'N/A')}")
    print(f"   ✓ Method: {search_results.get('method', 'N/A')}")
    print(f"   ✓ Confidence: {search_results.get('confidence', 0):.2f}")
    print(f"   ✓ Answer: {search_results.get('answer', 'No answer')[:100]}...")
    
    return processor


async def advanced_chunking_example():
    """Demonstrate different chunking strategies."""
    print("\n" + "="*60)
    print("ADVANCED CHUNKING EXAMPLE")
    print("="*60)
    
    strategies = ["fixed_size", "sentence_based", "paragraph_based", "semantic_based", "hybrid"]
    
    for strategy in strategies:
        print(f"\n--- Testing {strategy} chunking ---")
        
        processor = create_enhanced_standalone_processor(
            chunking_strategy=strategy,
            chunk_size=800,
            chunk_overlap=150
        )
        
        # Process the same document with different strategies
        metadata = processor.process_document(
            file_path="documents/sample_document.md",
            enable_chunking=True
        )
        
        print(f"   Strategy: {strategy}")
        print(f"   Processing time: {metadata.processing_duration_seconds:.2f}s")
        print(f"   Success: {'✓' if not metadata.errors else '✗'}")


async def hybrid_search_comparison():
    """Compare different search methods."""
    print("\n" + "="*60)
    print("HYBRID SEARCH COMPARISON")
    print("="*60)
    
    processor = create_enhanced_standalone_processor(
        enable_hybrid_search=True
    )
    
    query = "What systems does Mike Johnson work with?"
    search_methods = ["semantic_only", "graph_only", "template_hybrid", "discovery_hybrid", "auto_hybrid"]
    
    for method in search_methods:
        print(f"\n--- {method.upper()} SEARCH ---")
        
        try:
            results = await processor.search(
                query=query,
                method=method,
                top_k=5
            )
            
            print(f"   Method: {results.get('method', 'N/A')}")
            print(f"   Confidence: {results.get('confidence', 0):.2f}")
            
            # Show semantic results
            semantic_results = results.get('semantic_results', {})
            print(f"   Semantic chunks: {semantic_results.get('total_chunks', 0)}")
            
            # Show graph results
            graph_results = results.get('graph_results', {})
            print(f"   Graph relationships: {graph_results.get('total_relationships', 0)}")
            
            # Show answer preview
            answer = results.get('answer', 'No answer')
            print(f"   Answer preview: {answer[:100]}...")
            
        except Exception as e:
            print(f"   Error: {e}")


async def entity_discovery_example():
    """Demonstrate entity discovery capabilities."""
    print("\n" + "="*60)
    print("ENTITY DISCOVERY EXAMPLE")
    print("="*60)
    
    processor = create_enhanced_standalone_processor(
        enable_entity_discovery=True
    )
    
    # Create mock search results for entity discovery
    search_results = [
        {
            "text": "John Doe is the project manager for Project Alpha. He works closely with the development team.",
            "metadata": {
                "chunk_text": "John Doe is the project manager for Project Alpha. He works closely with the development team.",
                "file_id": "project_alpha_status.md"
            }
        },
        {
            "text": "Sarah Smith leads the development team and integrates with the CRM System.",
            "metadata": {
                "chunk_text": "Sarah Smith leads the development team and integrates with the CRM System.",
                "file_id": "team_assignments.md"
            }
        },
        {
            "text": "The Analytics Dashboard connects to the CRM System and provides real-time insights.",
            "metadata": {
                "chunk_text": "The Analytics Dashboard connects to the CRM System and provides real-time insights.",
                "file_id": "system_architecture.md"
            }
        }
    ]
    
    print("\n1. Discovering entities from search results...")
    discovered_entities = processor.discover_entities(search_results)
    
    print(f"   ✓ Discovered {len(discovered_entities)} entities:")
    for entity in discovered_entities[:5]:  # Show top 5
        print(f"     - {entity['name']} ({entity['type']}) - Confidence: {entity['confidence']:.2f}")
        if entity['aliases']:
            print(f"       Aliases: {', '.join(entity['aliases'])}")


async def knowledge_enrichment_example():
    """Demonstrate query enrichment capabilities."""
    print("\n" + "="*60)
    print("KNOWLEDGE ENRICHMENT EXAMPLE")
    print("="*60)
    
    processor = create_enhanced_standalone_processor(
        enable_knowledge_enrichment=True
    )
    
    queries = [
        "Who is working on Project Alpha?",
        "What systems does Mike Johnson use?",
        "Who manages the development team?",
        "How are the CRM and Analytics systems connected?"
    ]
    
    for query in queries:
        print(f"\n--- Enriching query: '{query}' ---")
        
        enriched_queries = processor.enrich_query(query)
        
        print(f"   Generated {len(enriched_queries)} enriched queries:")
        for i, eq in enumerate(enriched_queries[:3], 1):  # Show top 3
            print(f"     {i}. Template: {eq['template']}")
            print(f"        Confidence: {eq['confidence']:.2f}")
            print(f"        Explanation: {eq['explanation']}")


async def performance_comparison():
    """Compare performance with different configurations."""
    print("\n" + "="*60)
    print("PERFORMANCE COMPARISON")
    print("="*60)
    
    import time
    
    configurations = [
        {
            "name": "Basic (no enhancements)",
            "config": {
                "enable_hybrid_search": False,
                "enable_entity_discovery": False,
                "enable_knowledge_enrichment": False,
                "chunking_strategy": "fixed_size"
            }
        },
        {
            "name": "Enhanced (all features)",
            "config": {
                "enable_hybrid_search": True,
                "enable_entity_discovery": True,
                "enable_knowledge_enrichment": True,
                "chunking_strategy": "hybrid"
            }
        }
    ]
    
    for config in configurations:
        print(f"\n--- {config['name']} ---")
        
        start_time = time.time()
        
        processor = create_enhanced_standalone_processor(**config['config'])
        
        # Process a document
        metadata = processor.process_document(
            file_path="documents/sample_document.md",
            enable_chunking=True
        )
        
        # Perform a search
        search_results = await processor.search(
            query="project management",
            top_k=5
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"   Processing time: {metadata.processing_duration_seconds:.2f}s")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Success: {'✓' if not metadata.errors else '✗'}")
        print(f"   Search results: {len(search_results.get('results', []))}")


async def directory_processing_example():
    """Demonstrate processing entire directories."""
    print("\n" + "="*60)
    print("DIRECTORY PROCESSING EXAMPLE")
    print("="*60)
    
    processor = create_enhanced_standalone_processor(
        chunking_strategy="hybrid",
        enable_hybrid_search=True,
        enable_entity_discovery=True
    )
    
    print("\n1. Processing entire documents directory...")
    
    results = processor.process_directory(
        directory_path="documents",
        file_patterns=['.md', '.txt', '.docx', '.json'],
        enable_chunking=True
    )
    
    print(f"   ✓ Processed {len(results)} documents")
    
    successful = sum(1 for r in results if not r.errors)
    failed = len(results) - successful
    
    print(f"   ✓ Successful: {successful}")
    print(f"   ✗ Failed: {failed}")
    
    total_time = sum(r.processing_duration_seconds for r in results)
    print(f"   ⏱ Total processing time: {total_time:.2f}s")
    
    # Show processing details
    for result in results[:3]:  # Show first 3
        print(f"     - {result.document_id}: {result.processing_duration_seconds:.2f}s")


async def main():
    """Run all examples."""
    print("🚀 Enhanced Enterprise KG Minimal - Examples")
    print("=" * 80)
    
    try:
        # Run examples
        await basic_usage_example()
        await advanced_chunking_example()
        await hybrid_search_comparison()
        await entity_discovery_example()
        await knowledge_enrichment_example()
        await performance_comparison()
        await directory_processing_example()
        
        print("\n" + "="*80)
        print("✅ All examples completed successfully!")
        print("="*80)
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        logger.error(f"Example execution failed: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
