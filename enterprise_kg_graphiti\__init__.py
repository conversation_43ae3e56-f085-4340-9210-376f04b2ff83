"""
Enterprise Knowledge Graph with Graphiti Integration

This package provides an enterprise-ready knowledge graph implementation
that leverages Graphiti's capabilities while maintaining compliance with
defined schema ontologies.

Key Features:
- Schema-aware entity and relationship extraction
- Efficient document processing (PDF, DOCX, TXT, JSON)
- Chunk-based processing for large documents
- Enterprise search capabilities
- Integration with existing ontology definitions
"""

from .core.processor import GraphitiEnterpriseProcessor
from .core.schema_adapter import SchemaAdapter
from .core.document_processor import DocumentProcessor
from .core.search_interface import SearchInterface

__version__ = "1.0.0"
__author__ = "Enterprise KG Team"

__all__ = [
    "GraphitiEnterpriseProcessor",
    "SchemaAdapter", 
    "DocumentProcessor",
    "SearchInterface"
]
