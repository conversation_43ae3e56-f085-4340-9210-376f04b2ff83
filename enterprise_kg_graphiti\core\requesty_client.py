"""
Requesty API Client for Enterprise Knowledge Graph

This module provides a custom LLM client that integrates with Requesty API
for use with Graphiti's knowledge graph system.
"""

import os
import logging
import json
from typing import Any, Dict, List, Optional, Union
import httpx
from pydantic import BaseModel
from graphiti_core.llm_client.client import LLMClient
from graphiti_core.llm_client.config import LLMConfig, ModelSize, DEFAULT_MAX_TOKENS
from graphiti_core.prompts.models import Message

logger = logging.getLogger(__name__)


class RequestyClient(LLMClient):
    """
    Custom LLM client for Requesty API integration.

    This client provides compatibility with Graphiti's LLMClient interface
    while using Requesty's API endpoints.
    """

    def __init__(self,
                 config: LLMConfig = None,
                 cache: bool = False,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None,
                 model: str = None,
                 temperature: float = 0.1,
                 max_tokens: int = 4000):
        """
        Initialize the Requesty client.

        Args:
            config: LLM configuration object (optional)
            cache: Whether to enable caching (not implemented for Requesty)
            api_key: Requesty API key (defaults to REQUESTY_API_KEY env var)
            base_url: Requesty base URL (defaults to REQUESTY_BASE_URL env var)
            model: Model name to use
            temperature: Temperature for generation
            max_tokens: Maximum tokens for generation
        """
        # Get model from environment if not provided
        if model is None:
            model = os.getenv('LLM_MODEL', 'openai/gpt-4o')

        # Create config if not provided
        if config is None:
            config = LLMConfig(
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            )

        # Initialize parent class
        super().__init__(config, cache)

        # Set Requesty-specific attributes
        self.api_key = api_key or os.getenv('REQUESTY_API_KEY')
        self.base_url = base_url or os.getenv('REQUESTY_BASE_URL', 'https://router.requesty.ai/v1')

        if not self.api_key:
            raise ValueError("Requesty API key is required. Set REQUESTY_API_KEY environment variable.")

        # Remove trailing slash from base_url
        self.base_url = self.base_url.rstrip('/')

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            headers={
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            },
            timeout=60.0
        )

        logger.info(f"Initialized Requesty client with model: {self.model}")

    def _safe_json_parse(self, text: str) -> Dict[str, Any]:
        """
        Safely parse JSON from text, handling malformed responses.

        Args:
            text: Raw text that may contain JSON

        Returns:
            Parsed JSON dictionary
        """
        try:
            # First try direct parsing
            return json.loads(text)
        except json.JSONDecodeError as e:
            logger.warning(f"Direct JSON parsing failed: {e}")
            logger.debug(f"Raw response text: {text[:500]}...")

            # Try to find and extract valid JSON
            import re

            # Look for JSON objects (starting with { and ending with })
            json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            matches = re.findall(json_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

            # Try to extract JSON from code blocks
            code_block_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
            code_matches = re.findall(code_block_pattern, text, re.DOTALL)

            for match in code_matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

            # If all else fails, try to clean the text and parse again
            # Remove common extra characters that might appear after JSON
            cleaned_text = text.strip()

            # Find the last } and truncate there
            last_brace = cleaned_text.rfind('}')
            if last_brace != -1:
                truncated = cleaned_text[:last_brace + 1]
                try:
                    return json.loads(truncated)
                except json.JSONDecodeError:
                    pass

            # If we still can't parse, return the text as content
            logger.error(f"Could not extract valid JSON from response: {text[:200]}...")
            return {"content": text}

    async def _generate_response(self,
                               messages: List[Message],
                               response_model: Optional[BaseModel] = None,
                               max_tokens: int = DEFAULT_MAX_TOKENS,
                               model_size: ModelSize = ModelSize.medium) -> Dict[str, Any]:
        """
        Generate a response using Requesty API (required by LLMClient).

        Args:
            messages: List of Message objects
            response_model: Optional Pydantic model for structured output
            max_tokens: Maximum tokens to generate
            model_size: Model size (ignored for Requesty)

        Returns:
            Dictionary containing the response
        """
        try:
            # Convert Message objects to dict format for API
            api_messages = []
            for msg in messages:
                api_messages.append({
                    'role': msg.role,
                    'content': msg.content
                })

            # Prepare the request payload
            payload = {
                'model': self.model,
                'messages': api_messages,
                'temperature': self.temperature,
                'max_tokens': max_tokens
            }

            # Make the API request
            response = await self.client.post(
                f'{self.base_url}/chat/completions',
                json=payload
            )

            response.raise_for_status()

            # Get raw response text first for debugging
            response_text = response.text
            logger.debug(f"Raw Requesty response: {response_text[:500]}...")

            # Try to parse JSON safely
            try:
                result = json.loads(response_text)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse Requesty response as JSON: {e}")
                logger.error(f"Response text: {response_text}")
                # Try to extract valid JSON from the response
                result = self._safe_json_parse(response_text)
                if "content" in result and not isinstance(result.get("choices"), list):
                    # If we couldn't parse the API response, but got content, wrap it properly
                    result = {
                        "choices": [{"message": {"content": result["content"]}}]
                    }

            # Extract the generated text
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']

                # If response_model is specified, try to parse as JSON
                if response_model:
                    logger.debug(f"Content passed to _safe_json_parse for structured response: {content[:500]}...")
                    parsed_content = self._safe_json_parse(content)
                    logger.debug(f"Returning parsed_content for structured response: {str(parsed_content)[:500]}...")
                    return parsed_content
                else:
                    # Return as text for non-structured responses
                    logger.debug(f"Returning content for non-structured response: {content[:500]}...")
                    return {"content": content}
            else:
                logger.error(f"No choices in Requesty response: {result}")
                raise ValueError("No response generated from Requesty API")

        except httpx.HTTPStatusError as e:
            logger.error(f"Requesty API HTTP error: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Error generating response with Requesty: {e}")
            raise



    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()

    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self.close())
        except:
            pass


class RequestyConfig(LLMConfig):
    """Configuration for Requesty client."""

    def __init__(self,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None,
                 model: str = "openai/gpt-4o",
                 temperature: float = 0.1,
                 max_tokens: int = 4000):
        self.api_key = api_key or os.getenv('REQUESTY_API_KEY')
        self.base_url = base_url or os.getenv('REQUESTY_BASE_URL', 'https://router.requesty.ai/v1')
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens


def create_requesty_client() -> RequestyClient:
    """
    Factory function to create a Requesty client with environment configuration.

    Returns:
        Configured RequestyClient instance
    """
    # Create LLM config
    config = LLMConfig(
        model=os.getenv('LLM_MODEL', 'openai/gpt-4o'),
        temperature=float(os.getenv('LLM_TEMPERATURE', '0.1')),
        max_tokens=int(os.getenv('LLM_MAX_TOKENS', '4000'))
    )

    return RequestyClient(
        config=config,
        api_key=os.getenv('REQUESTY_API_KEY'),
        base_url=os.getenv('REQUESTY_BASE_URL')
    )
