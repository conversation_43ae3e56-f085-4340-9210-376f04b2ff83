#!/usr/bin/env python3
"""
Test JSON processing functionality.
"""

import os
import sys
import json

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from constants.json_processing import (
    detect_json_source_type,
    flatten_json_for_processing,
    generate_json_context_description
)


def test_json_processing():
    """Test JSON processing with the Jira file."""
    print("🧪 Testing JSON Processing")
    print("=" * 40)
    
    json_file = "documents/JiraSource.json"
    
    if not os.path.exists(json_file):
        print(f"❌ JSON file not found: {json_file}")
        return False
    
    try:
        # Load JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"✅ Loaded JSON file: {json_file}")
        print(f"📊 JSON keys: {list(json_data.keys())}")
        
        # Test source type detection
        source_type = detect_json_source_type(json_data)
        print(f"🔍 Detected source type: {source_type.value}")
        
        # Test context description
        context_desc = generate_json_context_description(json_data, source_type)
        print(f"📝 Context description: {context_desc}")
        
        # Test flattening
        statements = flatten_json_for_processing(json_data)
        print(f"📋 Generated {len(statements)} statements:")
        for i, stmt in enumerate(statements[:10]):  # Show first 10
            print(f"   {i+1}. {stmt}")
        if len(statements) > 10:
            print(f"   ... and {len(statements) - 10} more")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing JSON: {e}")
        return False


def test_document_reading():
    """Test the _read_json method."""
    print("\n🧪 Testing Document Reading")
    print("=" * 40)
    
    try:
        from standalone_processor import StandaloneDocumentProcessor
        
        # Create a minimal processor instance for testing
        class TestProcessor:
            def _read_json(self, file_path: str) -> str:
                """Test the _read_json method."""
                try:
                    # Import JSON processing utilities
                    from constants.json_processing import (
                        detect_json_source_type,
                        flatten_json_for_processing,
                        generate_json_context_description
                    )
                    
                    # Load and parse JSON
                    with open(file_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    
                    # Detect the type of JSON source
                    source_type = detect_json_source_type(json_data)
                    print(f"Detected JSON source type: {source_type.value}")
                    
                    # Generate context description
                    context_description = generate_json_context_description(json_data, source_type)
                    
                    # Flatten JSON into processable text statements
                    statements = flatten_json_for_processing(json_data)
                    
                    # Combine into formatted text for LLM processing
                    formatted_content = [
                        f"JSON Document Analysis",
                        f"Source Type: {source_type.value}",
                        f"Context: {context_description}",
                        "",
                        "Document Content:",
                        "=" * 50
                    ]
                    
                    # Add flattened statements
                    formatted_content.extend(statements)
                    
                    # Add raw JSON for reference (truncated if too long)
                    formatted_content.extend([
                        "",
                        "Raw JSON Structure:",
                        "-" * 30
                    ])
                    
                    # Add formatted JSON (pretty printed)
                    json_str = json.dumps(json_data, indent=2, ensure_ascii=False)
                    if len(json_str) > 2000:  # Truncate very long JSON
                        json_str = json_str[:2000] + "\n... (truncated)"
                    
                    formatted_content.append(json_str)
                    
                    return '\n'.join(formatted_content)

                except Exception as e:
                    print(f"Failed to read JSON {file_path}: {e}")
                    raise
        
        processor = TestProcessor()
        content = processor._read_json("documents/JiraSource.json")
        
        print("📄 Generated content for LLM:")
        print("-" * 50)
        print(content[:1000] + "..." if len(content) > 1000 else content)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing document reading: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 JSON Processing Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: JSON processing utilities
    if test_json_processing():
        tests_passed += 1
    
    # Test 2: Document reading
    if test_document_reading():
        tests_passed += 1
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! JSON processing is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
