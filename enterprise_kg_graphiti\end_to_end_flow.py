"""
End-to-End Flow for Enterprise Knowledge Graph with Graphiti

This script demonstrates the complete workflow from document processing
to knowledge graph creation and querying using both Cypher and hybrid search.
"""

import asyncio
import logging
import os
from pathlib import Path
from typing import List, Dict, Any

from core.processor import GraphitiEnterpriseProcessor
from config import EnterpriseKGConfig
from utils.helpers import setup_logging, print_banner

# Set up logging
setup_logging("DEBUG")
logger = logging.getLogger(__name__)


class EnterpriseKGFlow:
    """
    Complete end-to-end flow for enterprise knowledge graph processing.
    """
    
    def __init__(self):
        """Initialize the flow with configuration."""
        self.config = EnterpriseKGConfig()
        self.processor = None

    async def _clear_graph(self, driver):
        """Clear all nodes and relationships from the graph."""
        print_banner("Clearing Existing Graph Data (for testing)")
        try:
            async with driver.session() as session:
                await session.run("MATCH (n) DETACH DELETE n")
                print("✅ Graph data cleared successfully.")
        except Exception as e:
            print(f"❌ Failed to clear graph data: {e}")
            logger.error(f"Error clearing graph: {e}")
            # Optionally, decide if this should be a fatal error for the script
            # raise

    async def initialize(self):
        """Initialize the processor and knowledge graph."""
        print_banner("Initializing Enterprise Knowledge Graph")
        
        # Validate configuration
        errors = self.config.validate()
        if errors:
            print("❌ Configuration errors:")
            for error in errors:
                print(f"  - {error}")
            raise ValueError("Configuration validation failed")
        
        # Initialize processor
        self.processor = GraphitiEnterpriseProcessor(
            neo4j_uri=self.config.neo4j.uri,
            neo4j_user=self.config.neo4j.user,
            neo4j_password=self.config.neo4j.password,
            chunk_size=self.config.processing.chunk_size,
            chunk_overlap=self.config.processing.chunk_overlap,
            store_raw_content=self.config.processing.store_raw_content
        )
        
        # Clear graph before initializing processor's indices
        # This uses the driver from the newly created processor instance
        # Or, we could create a temporary driver instance here if processor isn't fully ready
        # For simplicity, let's assume processor.graphiti.driver is available after __init__
        # However, graphiti.driver is only set after Graphiti.__init__
        # A safer way is to get driver from config directly for clearing.
        
        from neo4j import AsyncGraphDatabase # Import for direct driver usage
        temp_driver = None
        try:
            temp_driver = AsyncGraphDatabase.driver(
                self.config.neo4j.uri,
                auth=(self.config.neo4j.user, self.config.neo4j.password)
            )
            await self._clear_graph(temp_driver)
        finally:
            if temp_driver:
                await temp_driver.close()
        
        await self.processor.initialize() # This will build indices on a clean graph
        print("✅ Knowledge graph initialized successfully")
        
    async def process_documents(self, documents_path: str) -> List[Dict[str, Any]]:
        """
        Process documents and create knowledge graph.
        
        Args:
            documents_path: Path to documents directory
            
        Returns:
            List of processing results
        """
        print_banner("Processing Documents")
        
        if not Path(documents_path).exists():
            raise FileNotFoundError(f"Documents path not found: {documents_path}")
        
        # Process all documents
        results = await self.processor.process_directory(
            documents_path,
            self.config.processing.supported_extensions
        )
        
        # Print summary
        successful = sum(1 for r in results if r['success'])
        total_episodes = sum(r.get('episodes_added', 0) for r in results)
        
        print(f"📊 Processing Summary:")
        print(f"   Files processed: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Episodes created: {total_episodes}")
        
        if successful == 0:
            print("⚠️ No documents were processed successfully")
        
        return results
    
    async def demonstrate_cypher_queries(self):
        """Demonstrate Cypher queries on the knowledge graph."""
        print_banner("Cypher Query Examples")
        
        # Get Neo4j driver for direct queries
        driver = self.processor.graphiti.driver
        
        cypher_queries = [
            {
                "name": "File Sources and Their Content",
                "query": """
                MATCH (f:FileSource)-[r:CONTAINS]->(content)
                RETURN f.name as file_name, 
                       f.document_type as doc_type,
                       f.total_chunks as chunks,
                       type(r) as relationship,
                       labels(content) as content_type,
                       count(content) as content_count
                ORDER BY f.name
                """,
                "description": "Shows all file sources and what they contain"
            },
            {
                "name": "Entity Extraction Summary",
                "query": """
                MATCH (f:FileSource)-[:CONTAINS]->(e:Entity)
                RETURN f.name as source_file,
                       e.name as entity_name,
                       labels(e) as entity_labels
                ORDER BY f.name, e.name
                LIMIT 20
                """,
                "description": "Shows entities extracted from each file"
            },
            {
                "name": "Entity Relationships",
                "query": """
                MATCH (e1:Entity)-[r]->(e2:Entity)
                RETURN e1.name as from_entity,
                       type(r) as relationship_type,
                       e2.name as to_entity,
                       r.fact as relationship_fact
                ORDER BY e1.name
                LIMIT 15
                """,
                "description": "Shows relationships between entities"
            },
            {
                "name": "Knowledge Graph Statistics",
                "query": """
                MATCH (n)
                WITH labels(n) as node_labels
                UNWIND node_labels as label
                RETURN label, count(*) as count
                ORDER BY count DESC
                """,
                "description": "Shows node type distribution in the graph"
            },
            {
                "name": "File Source Details",
                "query": """
                MATCH (f:FileSource)
                RETURN f.name as file_name,
                       f.document_type as type,
                       f.total_chunks as chunks,
                       f.created_at as processed_at
                ORDER BY f.created_at DESC
                """,
                "description": "Shows all processed files with metadata"
            }
        ]
        
        async with driver.session() as session:
            for query_info in cypher_queries:
                print(f"\n🔍 {query_info['name']}")
                print(f"   {query_info['description']}")
                print(f"   Query: {query_info['query'].strip()}")
                
                try:
                    result = await session.run(query_info['query'])
                    records = await result.data()
                    
                    if records:
                        print(f"   Results ({len(records)} rows):")
                        for i, record in enumerate(records[:5], 1):  # Show first 5 results
                            print(f"     {i}. {dict(record)}")
                        if len(records) > 5:
                            print(f"     ... and {len(records) - 5} more rows")
                    else:
                        print("   No results found")
                        
                except Exception as e:
                    print(f"   ❌ Query failed: {e}")
                
                print()
    
    async def demonstrate_hybrid_search(self):
        """Demonstrate hybrid search capabilities."""
        print_banner("Hybrid Search Examples")
        
        search_queries = [
            "company culture and values",
            "team collaboration",
            "leadership principles",
            "innovation and creativity",
            "employee development",
            "organizational structure",
            "business processes",
            "strategic goals"
        ]
        
        for query in search_queries:
            print(f"\n🔍 Searching: '{query}'")
            
            try:
                # Perform hybrid search
                results = await self.processor.search(query, limit=5)
                
                if results:
                    print(f"   Found {len(results)} results:")
                    for i, result in enumerate(results, 1):
                        fact = result.get('fact', result.get('name', 'Unknown'))
                        # Truncate long facts
                        if len(fact) > 100:
                            fact = fact[:97] + "..."
                        print(f"     {i}. {fact}")
                        
                        # Show additional metadata if available
                        if result.get('type') == 'relationship':
                            rel_type = result.get('relationship_type', 'Unknown')
                            print(f"        Type: {rel_type}")
                else:
                    print("   No results found")
                    
            except Exception as e:
                print(f"   ❌ Search failed: {e}")
    
    async def demonstrate_entity_relationships(self):
        """Demonstrate entity relationship queries."""
        print_banner("Entity Relationship Analysis")
        
        # First, get some entities to analyze
        driver = self.processor.graphiti.driver
        
        async with driver.session() as session:
            # Get some entities
            entity_query = """
            MATCH (e:Entity)
            RETURN e.name as entity_name
            ORDER BY e.name
            LIMIT 10
            """
            
            try:
                result = await session.run(entity_query)
                entities = await result.data()
                
                if entities:
                    print("🔍 Analyzing relationships for entities:")
                    
                    for entity_data in entities[:5]:  # Analyze first 5 entities
                        entity_name = entity_data['entity_name']
                        print(f"\n   Entity: {entity_name}")
                        
                        # Get relationships using the processor
                        relationships = await self.processor.get_entity_relationships(entity_name)
                        
                        if relationships:
                            print(f"     Found {len(relationships)} relationships:")
                            for i, rel in enumerate(relationships[:3], 1):  # Show first 3
                                fact = rel.get('fact', 'Unknown relationship')
                                if len(fact) > 80:
                                    fact = fact[:77] + "..."
                                print(f"       {i}. {fact}")
                        else:
                            print("     No relationships found")
                else:
                    print("No entities found in the knowledge graph")
                    
            except Exception as e:
                print(f"❌ Entity analysis failed: {e}")
    
    async def add_text_example(self, text: str, source_description: str = "Manual text input"):
        """
        Example of adding text directly to the knowledge graph.
        
        Args:
            text: Text content to add
            source_description: Description of the text source
        """
        print_banner("Adding Text to Knowledge Graph")
        
        print(f"📝 Adding text: '{text[:100]}{'...' if len(text) > 100 else ''}'")
        
        try:
            # Create a temporary file-like structure
            from core.document_processor import ProcessedDocument, DocumentChunk
            import hashlib
            
            # Generate IDs
            text_hash = hashlib.md5(text.encode()).hexdigest()
            doc_id = f"manual_text_{text_hash[:8]}"
            
            # Create document structure
            chunk = DocumentChunk(
                chunk_id=f"{doc_id}_chunk_0",
                text=text,
                start_position=0,
                end_position=len(text),
                chunk_index=0,
                source_file=f"manual_input_{text_hash[:8]}.txt",
                metadata={"source": "manual_input"}
            )
            
            document = ProcessedDocument(
                document_id=doc_id,
                source_file=f"manual_input_{text_hash[:8]}.txt",
                document_type="manual_text",
                total_chunks=1,
                chunks=[chunk],
                metadata={"source": "manual_input", "description": source_description}
            )
            
            # Add to knowledge graph
            success = await self.processor._add_chunk_as_episode(chunk, document)
            
            if success:
                print("✅ Text added successfully to knowledge graph")
                
                # Search for the added content
                print("\n🔍 Searching for the added content...")
                search_results = await self.processor.search(text[:50], limit=3)
                
                if search_results:
                    print(f"   Found {len(search_results)} related results:")
                    for i, result in enumerate(search_results, 1):
                        fact = result.get('fact', result.get('name', 'Unknown'))
                        if len(fact) > 80:
                            fact = fact[:77] + "..."
                        print(f"     {i}. {fact}")
                else:
                    print("   No related results found yet (may need time to index)")
            else:
                print("❌ Failed to add text to knowledge graph")
                
        except Exception as e:
            print(f"❌ Error adding text: {e}")
    
    async def cleanup(self):
        """Clean up resources."""
        if self.processor:
            await self.processor.close()
            print("✅ Resources cleaned up")


async def main():
    """Main function to run the complete end-to-end flow."""
    
    print_banner("Enterprise Knowledge Graph - End-to-End Flow")
    
    # Initialize flow
    flow = EnterpriseKGFlow()
    
    try:
        # Step 1: Initialize
        await flow.initialize()
        
        # Step 2: Process documents
        # Construct path relative to this script's location
        script_dir = Path(__file__).parent
        documents_path = script_dir / "sample_docs"
        if not documents_path.exists():
            print(f"❌ Test documents folder not found at {documents_path.resolve()}. Please ensure test_doc_1.txt and test_doc_2.txt are in enterprise_kg_graphiti/sample_docs/")
            return # Exit if test docs are not found
        
        print(f"ℹ️ Processing documents from: {documents_path.resolve()}")
        results = await flow.process_documents(str(documents_path)) # process_directory expects a string
        
        # Only proceed if we have successful processing
        successful_results = [r for r in results if r['success']]
        if not successful_results:
            print("❌ No documents were processed successfully. Cannot proceed with queries.")
            return
        
        print_banner("Document Processing Phase Complete for Testing.")
        print("Please check enterprise_kg_graphiti/logs/chunk_details.log and Neo4j.")

        # Commenting out further demonstration steps for focused testing
        # print("Skipping Cypher, Search, and other demonstrations for this focused test run.")
        # # Step 3: Demonstrate Cypher queries
        # await flow.demonstrate_cypher_queries()
        #
        # # Step 4: Demonstrate hybrid search
        # await flow.demonstrate_hybrid_search()
        #
        # # Step 5: Demonstrate entity relationships
        # await flow.demonstrate_entity_relationships()
        #
        # # Step 6: Example of adding text directly
        # sample_text = """
        # Our company values innovation, collaboration, and customer focus.
        # The engineering team works closely with the product team to deliver
        # high-quality solutions. John Smith leads the development team and
        # reports to Sarah Johnson, the VP of Engineering.
        # """
        #
        # await flow.add_text_example(sample_text, "Company values example")
        
        print_banner("End-to-End Test Processing Completed!")
        
        print("\n📋 Next Steps for Verification:")
        print("1. Use Neo4j Browser to explore the graph visually")
        print("2. Run custom Cypher queries for specific insights")
        print("3. Integrate hybrid search into your applications")
        print("4. Add more documents to expand the knowledge graph")
        print("5. Use the processor API for real-time document processing")
        
    except Exception as e:
        print(f"❌ Flow failed: {e}")
        logger.exception("Detailed error:")
        
    finally:
        # Cleanup
        await flow.cleanup()


if __name__ == "__main__":
    # Check environment variables
    required_vars = ['REQUESTY_API_KEY', 'NEO4J_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables and try again.")
        exit(1)
    
    # Run the flow
    asyncio.run(main())
