"""
Test script for Requesty API connection and functionality.

This script tests the Requesty API integration to identify and fix
the 404 error when adding chunks as episodes.
"""

import asyncio
import os
import logging
import json
from pathlib import Path
import httpx
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def test_requesty_api_direct():
    """Test Requesty API directly with httpx."""
    print("🔍 Testing Requesty API directly...")

    api_key = os.getenv('REQUESTY_API_KEY')
    base_url = os.getenv('REQUESTY_BASE_URL', 'https://router.requesty.ai/v1')

    if not api_key:
        print("❌ REQUESTY_API_KEY not found in environment")
        return False

    print(f"API Key: {api_key[:20]}...")
    print(f"Base URL: {base_url}")

    # Test different model names to find working ones
    models_to_test = [
        'openai/gpt-4o',
        'openai/gpt-4',
        'anthropic/claude-3-5-sonnet-20241022',
        'anthropic/claude-3.5-sonnet',
        'anthropic/claude-3-sonnet-20240229',
        'claude-3-5-sonnet-20241022',
        'claude-3.5-sonnet'
    ]

    for model in models_to_test:
        print(f"\n🧪 Testing model: {model}")

        # Test payload
        payload = {
            'model': model,
            'messages': [
                {'role': 'user', 'content': 'Hello, can you respond with a simple greeting?'}
            ],
            'temperature': 0.1,
            'max_tokens': 100
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f'{base_url}/chat/completions',
                    headers={
                        'Authorization': f'Bearer {api_key}',
                        'Content-Type': 'application/json'
                    },
                    json=payload,
                    timeout=30.0
                )

                print(f"Status Code: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Model {model} works!")
                    print(f"Response: {result['choices'][0]['message']['content'][:100]}...")
                    return True
                else:
                    print(f"❌ Model {model} failed: {response.status_code}")
                    print(f"Response: {response.text}")

        except Exception as e:
            print(f"❌ Exception with model {model}: {e}")

    print("❌ No working models found")
    return False


async def test_requesty_client():
    """Test the RequestyClient class."""
    print("\n🔍 Testing RequestyClient class...")

    try:
        from core.requesty_client import create_requesty_client
        from graphiti_core.prompts.models import Message

        # Create client using factory function
        client = create_requesty_client()
        print("✅ RequestyClient created successfully")

        # Test message
        messages = [
            Message(role='user', content='Hello, can you respond with a simple greeting?')
        ]

        # Test generation
        result = await client._generate_response(messages, max_tokens=100)
        print("✅ Response generated successfully!")
        print(f"Result: {result}")

        await client.close()
        return True

    except Exception as e:
        print(f"❌ RequestyClient test failed: {e}")
        logger.exception("Detailed error:")
        return False


async def test_graphiti_integration():
    """Test Graphiti integration with RequestyClient."""
    print("\n🔍 Testing Graphiti integration...")

    try:
        from core.requesty_client import create_requesty_client
        from core.simple_embedder import create_simple_embedder
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        from datetime import datetime, timezone

        # Create clients
        llm_client = create_requesty_client()
        embedder = create_simple_embedder()

        print("✅ Clients created successfully")

        # Create Graphiti instance
        graphiti = Graphiti(
            uri=os.getenv('NEO4J_URI'),
            user=os.getenv('NEO4J_USER'),
            password=os.getenv('NEO4J_PASSWORD'),
            llm_client=llm_client,
            embedder=embedder,
            store_raw_episode_content=True
        )

        print("✅ Graphiti instance created")

        # Initialize
        await graphiti.build_indices_and_constraints()
        print("✅ Graphiti initialized")

        # Test adding a simple episode
        episode_result = await graphiti.add_episode(
            name="test_episode",
            episode_body="This is a test episode to verify Requesty API integration.",
            source_description="Test episode for API verification",
            reference_time=datetime.now(timezone.utc),
            source=EpisodeType.text,
            group_id="test_group"
        )

        print("✅ Episode added successfully!")
        print(f"Episode UUID: {episode_result.episode.uuid}")

        await graphiti.close()
        await llm_client.close()

        return True

    except Exception as e:
        print(f"❌ Graphiti integration test failed: {e}")
        logger.exception("Detailed error:")
        return False


async def test_main_py_simulation():
    """Simulate the main.py workflow to identify the issue."""
    print("\n🔍 Simulating main.py workflow...")

    try:
        from core.processor import GraphitiEnterpriseProcessor
        from config import EnterpriseKGConfig

        # Load config
        config = EnterpriseKGConfig()

        # Create processor
        processor = GraphitiEnterpriseProcessor(
            neo4j_uri=config.neo4j.uri,
            neo4j_user=config.neo4j.user,
            neo4j_password=config.neo4j.password,
            chunk_size=config.processing.chunk_size,
            chunk_overlap=config.processing.chunk_overlap,
            store_raw_content=config.processing.store_raw_content
        )

        print("✅ Processor created")

        # Initialize
        await processor.initialize()
        print("✅ Processor initialized")

        # Create a test file
        test_file = Path("test_document.txt")
        test_file.write_text("This is a test document for verifying the Requesty API integration with Graphiti.")

        print(f"✅ Test file created: {test_file}")

        # Process the file
        result = await processor.process_file(str(test_file))
        print("✅ File processed successfully!")
        print(f"Result: {result}")

        # Clean up
        await processor.close()
        test_file.unlink()  # Delete test file

        return True

    except Exception as e:
        print(f"❌ Main.py simulation failed: {e}")
        logger.exception("Detailed error:")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Requesty API Tests")
    print("=" * 50)

    tests = [
        ("Direct API Test", test_requesty_api_direct),
        ("RequestyClient Test", test_requesty_client),
        ("Graphiti Integration Test", test_graphiti_integration),
        ("Main.py Simulation", test_main_py_simulation)
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = await test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False

    # Summary
    print(f"\n{'='*20} TEST SUMMARY {'='*20}")
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")

    total_tests = len(results)
    passed_tests = sum(results.values())
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")

    if passed_tests < total_tests:
        print("\n🔧 Issues found! Check the detailed output above for debugging information.")
    else:
        print("\n🎉 All tests passed! Requesty API integration is working correctly.")


if __name__ == "__main__":
    asyncio.run(main())
