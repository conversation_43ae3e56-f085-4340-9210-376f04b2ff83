name: MyPy Type Check

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

jobs:
  mypy:
    runs-on: depot-ubuntu-22.04
    environment: development
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        id: setup-python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.2
          virtualenvs-create: true
          virtualenvs-in-project: true
          installer-parallel: true
      - name: Install dependencies
        run: poetry install --no-interaction --with dev
      - name: Run MyPy for graphiti-core
        shell: bash
        run: |
          set -o pipefail
          poetry run mypy ./graphiti_core --show-column-numbers --show-error-codes | sed -E '
            s/^(.*):([0-9]+):([0-9]+): (error|warning): (.+) \[(.+)\]/::error file=\1,line=\2,endLine=\2,col=\3,title=\6::\5/;
            s/^(.*):([0-9]+):([0-9]+): note: (.+)/::notice file=\1,line=\2,endLine=\2,col=\3,title=Note::\4/;
          '
      - name: Install graph-service dependencies
        shell: bash
        run: |
          cd server
          poetry install --no-interaction --with dev
      - name: Run MyPy for graph-service
        shell: bash
        run: |
          cd server
          set -o pipefail
          poetry run mypy . --show-column-numbers --show-error-codes | sed -E '
            s/^(.*):([0-9]+):([0-9]+): (error|warning): (.+) \[(.+)\]/::error file=\1,line=\2,endLine=\2,col=\3,title=\6::\5/;
            s/^(.*):([0-9]+):([0-9]+): note: (.+)/::notice file=\1,line=\2,endLine=\2,col=\3,title=Note::\4/;
          '
