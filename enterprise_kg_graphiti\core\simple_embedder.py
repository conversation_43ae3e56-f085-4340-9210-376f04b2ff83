"""
Simple Embedder for Enterprise Knowledge Graph

This module provides a simple embedder that doesn't require external API calls
for basic functionality when embeddings are not critical.
"""

import logging
import hashlib
from typing import List, Union, Iterable
import numpy as np
from graphiti_core.embedder.client import EmbedderClient

logger = logging.getLogger(__name__)


class SimpleEmbedder(EmbedderClient):
    """
    Simple embedder that generates basic embeddings without external API calls.

    This is useful when you want to avoid embedding API costs or when
    embeddings are not critical for your use case.
    """

    def __init__(self, embedding_dim: int = 384):
        """
        Initialize the simple embedder.

        Args:
            embedding_dim: Dimension of the embedding vectors
        """
        self.embedding_dim = embedding_dim
        logger.info(f"Initialized SimpleEmbedder with dimension: {embedding_dim}")

    async def create(self, input_data: str | list[str] | Iterable[int] | Iterable[Iterable[int]]) -> list[float]:
        """
        Create embeddings for the given input data (required by EmbedderClient).

        Args:
            input_data: The input data to create embeddings for

        Returns:
            List of float values representing the embedding
        """
        if isinstance(input_data, str):
            return await self.embed_text(input_data)
        elif isinstance(input_data, list) and len(input_data) > 0:
            if isinstance(input_data[0], str):
                # List of strings - return embedding for first string
                return await self.embed_text(input_data[0])
            else:
                # List of other types - convert to string
                return await self.embed_text(str(input_data[0]))
        else:
            # Other iterables - convert to string
            return await self.embed_text(str(input_data))

    async def embed_text(self, text: str) -> List[float]:
        """
        Generate a simple embedding for text.

        This uses a hash-based approach to generate consistent embeddings
        for the same text while providing some semantic-like properties.

        Args:
            text: Text to embed

        Returns:
            List of float values representing the embedding
        """
        try:
            # Normalize text
            normalized_text = text.lower().strip()

            # Generate hash-based embedding
            embedding = self._hash_to_embedding(normalized_text)

            return embedding.tolist()

        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            # Return zero vector as fallback
            return [0.0] * self.embedding_dim

    async def create_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Create embeddings for a batch of texts (required by EmbedderClient).

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        return await self.embed_texts(texts)

    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts.

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        embeddings = []
        for text in texts:
            embedding = await self.embed_text(text)
            embeddings.append(embedding)
        return embeddings

    def _hash_to_embedding(self, text: str) -> np.ndarray:
        """
        Convert text to embedding using hash-based approach.

        Args:
            text: Input text

        Returns:
            Numpy array representing the embedding
        """
        # Create multiple hash values for different aspects
        hashes = []

        # Word-level hashes
        words = text.split()
        for i in range(min(len(words), 10)):  # Use up to 10 words
            word_hash = hashlib.md5(words[i].encode()).hexdigest()
            hashes.append(int(word_hash[:8], 16))

        # Character-level hashes
        for i in range(0, min(len(text), 50), 5):  # Sample every 5 characters
            char_chunk = text[i:i+5]
            char_hash = hashlib.md5(char_chunk.encode()).hexdigest()
            hashes.append(int(char_hash[:8], 16))

        # Length-based features
        hashes.append(len(text))
        hashes.append(len(words))
        hashes.append(len(set(words)))  # Unique words

        # Ensure we have enough values
        while len(hashes) < self.embedding_dim:
            # Generate additional hashes from text + index
            additional_hash = hashlib.md5(f"{text}_{len(hashes)}".encode()).hexdigest()
            hashes.append(int(additional_hash[:8], 16))

        # Convert to numpy array and normalize
        embedding = np.array(hashes[:self.embedding_dim], dtype=np.float32)

        # Normalize to [-1, 1] range
        embedding = embedding / (2**31)  # Normalize by max int32 value

        # Apply some smoothing to make it more embedding-like
        embedding = np.tanh(embedding)

        # Ensure unit vector (common in embeddings)
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm

        return embedding

    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this embedder."""
        return self.embedding_dim


class PineconeEmbedder(EmbedderClient):
    """
    Embedder that integrates with existing Pinecone setup.

    This can be used if you already have embeddings in Pinecone
    and want to reuse them.
    """

    def __init__(self, pinecone_index=None, embedding_dim: int = 384):
        """
        Initialize Pinecone embedder.

        Args:
            pinecone_index: Pinecone index instance (optional)
            embedding_dim: Dimension of embeddings
        """
        self.pinecone_index = pinecone_index
        self.embedding_dim = embedding_dim
        self.simple_embedder = SimpleEmbedder(embedding_dim)

        logger.info("Initialized PineconeEmbedder")

    async def create(self, input_data: str | list[str] | Iterable[int] | Iterable[Iterable[int]]) -> list[float]:
        """
        Create embeddings for the given input data (required by EmbedderClient).

        Args:
            input_data: The input data to create embeddings for

        Returns:
            List of float values representing the embedding
        """
        if isinstance(input_data, str):
            return await self.embed_text(input_data)
        elif isinstance(input_data, list) and len(input_data) > 0:
            if isinstance(input_data[0], str):
                return await self.embed_text(input_data[0])
            else:
                return await self.embed_text(str(input_data[0]))
        else:
            return await self.embed_text(str(input_data))

    async def embed_text(self, text: str) -> List[float]:
        """
        Generate embedding, preferring Pinecone if available.

        Args:
            text: Text to embed

        Returns:
            Embedding vector
        """
        try:
            if self.pinecone_index:
                # Try to retrieve from Pinecone first
                text_hash = hashlib.md5(text.encode()).hexdigest()

                # Query Pinecone for existing embedding
                try:
                    query_result = self.pinecone_index.query(
                        id=text_hash,
                        top_k=1,
                        include_values=True
                    )

                    if query_result.matches:
                        return query_result.matches[0].values
                except:
                    pass  # Fall back to simple embedder

            # Use simple embedder as fallback
            return await self.simple_embedder.embed_text(text)

        except Exception as e:
            logger.error(f"Error in PineconeEmbedder: {e}")
            return await self.simple_embedder.embed_text(text)

    async def create_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Create embeddings for a batch of texts (required by EmbedderClient).

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        return await self.embed_texts(texts)

    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts."""
        embeddings = []
        for text in texts:
            embedding = await self.embed_text(text)
            embeddings.append(embedding)
        return embeddings

    def get_embedding_dimension(self) -> int:
        """Get embedding dimension."""
        return self.embedding_dim


def create_simple_embedder() -> SimpleEmbedder:
    """
    Factory function to create a simple embedder.

    Returns:
        SimpleEmbedder instance
    """
    return SimpleEmbedder(embedding_dim=384)


def create_pinecone_embedder(pinecone_index=None) -> PineconeEmbedder:
    """
    Factory function to create a Pinecone-integrated embedder.

    Args:
        pinecone_index: Optional Pinecone index instance

    Returns:
        PineconeEmbedder instance
    """
    return PineconeEmbedder(pinecone_index=pinecone_index, embedding_dim=384)
