"""
Advanced Chunking Engine for Enterprise KG Minimal

This module provides sophisticated document chunking strategies that can be used
independently or integrated with the standalone processor. It supports multiple
chunking approaches optimized for knowledge graph extraction.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import hashlib


logger = logging.getLogger(__name__)


class ChunkingStrategy(Enum):
    """Available chunking strategies."""
    FIXED_SIZE = "fixed_size"
    SENTENCE_BASED = "sentence_based"
    PARAGRAPH_BASED = "paragraph_based"
    SEMANTIC_BASED = "semantic_based"
    HYBRID = "hybrid"


@dataclass
class ChunkMetadata:
    """Metadata for a document chunk."""
    chunk_id: str
    chunk_index: int
    start_position: int
    end_position: int
    word_count: int
    sentence_count: int
    source_file: str
    strategy_used: str
    overlap_with_previous: bool = False
    overlap_with_next: bool = False
    semantic_topic: Optional[str] = None
    confidence_score: float = 1.0


@dataclass
class DocumentChunk:
    """Represents a chunk of document content with rich metadata."""
    text: str
    metadata: ChunkMetadata
    
    def __len__(self) -> int:
        return len(self.text)
    
    def get_hash(self) -> str:
        """Generate a unique hash for this chunk."""
        return hashlib.md5(self.text.encode('utf-8')).hexdigest()


class ChunkingEngine:
    """
    Advanced chunking engine with multiple strategies for optimal knowledge extraction.
    
    Features:
    - Multiple chunking strategies
    - Configurable chunk sizes and overlaps
    - Sentence and paragraph boundary preservation
    - Semantic coherence optimization
    - Rich metadata generation
    """
    
    def __init__(
        self,
        strategy: ChunkingStrategy = ChunkingStrategy.HYBRID,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        min_chunk_size: int = 100,
        max_chunk_size: int = 2000,
        preserve_sentences: bool = True,
        preserve_paragraphs: bool = True
    ):
        """
        Initialize the chunking engine.
        
        Args:
            strategy: Primary chunking strategy to use
            chunk_size: Target size for chunks (in characters)
            chunk_overlap: Overlap between consecutive chunks
            min_chunk_size: Minimum acceptable chunk size
            max_chunk_size: Maximum acceptable chunk size
            preserve_sentences: Whether to avoid breaking sentences
            preserve_paragraphs: Whether to prefer paragraph boundaries
        """
        self.strategy = strategy
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.min_chunk_size = min_chunk_size
        self.max_chunk_size = max_chunk_size
        self.preserve_sentences = preserve_sentences
        self.preserve_paragraphs = preserve_paragraphs
        
        # Compile regex patterns for efficiency
        self.sentence_pattern = re.compile(r'[.!?]+\s+')
        self.paragraph_pattern = re.compile(r'\n\s*\n')
        self.word_pattern = re.compile(r'\b\w+\b')
    
    def chunk_document(
        self, 
        content: str, 
        source_file: str = "unknown",
        document_id: Optional[str] = None
    ) -> List[DocumentChunk]:
        """
        Chunk a document using the configured strategy.
        
        Args:
            content: Document content to chunk
            source_file: Source file path for metadata
            document_id: Optional document identifier
            
        Returns:
            List of document chunks with metadata
        """
        if not content or not content.strip():
            logger.warning(f"Empty content provided for {source_file}")
            return []
        
        logger.info(f"Chunking document {source_file} using {self.strategy.value} strategy")
        
        # Choose chunking method based on strategy
        if self.strategy == ChunkingStrategy.FIXED_SIZE:
            chunks = self._chunk_fixed_size(content, source_file)
        elif self.strategy == ChunkingStrategy.SENTENCE_BASED:
            chunks = self._chunk_sentence_based(content, source_file)
        elif self.strategy == ChunkingStrategy.PARAGRAPH_BASED:
            chunks = self._chunk_paragraph_based(content, source_file)
        elif self.strategy == ChunkingStrategy.SEMANTIC_BASED:
            chunks = self._chunk_semantic_based(content, source_file)
        else:  # HYBRID
            chunks = self._chunk_hybrid(content, source_file)
        
        logger.info(f"Created {len(chunks)} chunks for {source_file}")
        return chunks
    
    def _chunk_fixed_size(self, content: str, source_file: str) -> List[DocumentChunk]:
        """Create fixed-size chunks with overlap."""
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(content):
            # Calculate end position
            end = min(start + self.chunk_size, len(content))
            
            # Adjust end to preserve sentences if enabled
            if self.preserve_sentences and end < len(content):
                end = self._find_sentence_boundary(content, end, backward=True)
            
            # Extract chunk text
            chunk_text = content[start:end].strip()
            
            # Skip if chunk is too small
            if len(chunk_text) < self.min_chunk_size:
                break
            
            # Create metadata
            metadata = self._create_chunk_metadata(
                chunk_text, chunk_index, start, end, source_file, "fixed_size"
            )
            
            # Create chunk
            chunk = DocumentChunk(text=chunk_text, metadata=metadata)
            chunks.append(chunk)
            
            # Move to next position with overlap
            start = max(start + self.chunk_size - self.chunk_overlap, end)
            chunk_index += 1
        
        return chunks
    
    def _chunk_sentence_based(self, content: str, source_file: str) -> List[DocumentChunk]:
        """Create chunks based on sentence boundaries."""
        sentences = self._split_into_sentences(content)
        chunks = []
        current_chunk = []
        current_size = 0
        chunk_index = 0
        
        for sentence in sentences:
            sentence_size = len(sentence)
            
            # If adding this sentence would exceed max size, finalize current chunk
            if current_size + sentence_size > self.max_chunk_size and current_chunk:
                chunk_text = ' '.join(current_chunk).strip()
                if len(chunk_text) >= self.min_chunk_size:
                    start_pos = content.find(current_chunk[0])
                    end_pos = start_pos + len(chunk_text)
                    
                    metadata = self._create_chunk_metadata(
                        chunk_text, chunk_index, start_pos, end_pos, source_file, "sentence_based"
                    )
                    
                    chunks.append(DocumentChunk(text=chunk_text, metadata=metadata))
                    chunk_index += 1
                
                # Start new chunk with overlap
                overlap_sentences = self._get_overlap_sentences(current_chunk)
                current_chunk = overlap_sentences + [sentence]
                current_size = sum(len(s) for s in current_chunk)
            else:
                current_chunk.append(sentence)
                current_size += sentence_size
        
        # Handle remaining sentences
        if current_chunk:
            chunk_text = ' '.join(current_chunk).strip()
            if len(chunk_text) >= self.min_chunk_size:
                start_pos = content.find(current_chunk[0])
                end_pos = start_pos + len(chunk_text)
                
                metadata = self._create_chunk_metadata(
                    chunk_text, chunk_index, start_pos, end_pos, source_file, "sentence_based"
                )
                
                chunks.append(DocumentChunk(text=chunk_text, metadata=metadata))
        
        return chunks
    
    def _chunk_paragraph_based(self, content: str, source_file: str) -> List[DocumentChunk]:
        """Create chunks based on paragraph boundaries."""
        paragraphs = self.paragraph_pattern.split(content)
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        chunks = []
        current_chunk = []
        current_size = 0
        chunk_index = 0
        
        for paragraph in paragraphs:
            paragraph_size = len(paragraph)
            
            # If adding this paragraph would exceed target size, finalize current chunk
            if current_size + paragraph_size > self.chunk_size and current_chunk:
                chunk_text = '\n\n'.join(current_chunk).strip()
                if len(chunk_text) >= self.min_chunk_size:
                    start_pos = content.find(current_chunk[0])
                    end_pos = start_pos + len(chunk_text)
                    
                    metadata = self._create_chunk_metadata(
                        chunk_text, chunk_index, start_pos, end_pos, source_file, "paragraph_based"
                    )
                    
                    chunks.append(DocumentChunk(text=chunk_text, metadata=metadata))
                    chunk_index += 1
                
                # Start new chunk
                current_chunk = [paragraph]
                current_size = paragraph_size
            else:
                current_chunk.append(paragraph)
                current_size += paragraph_size
        
        # Handle remaining paragraphs
        if current_chunk:
            chunk_text = '\n\n'.join(current_chunk).strip()
            if len(chunk_text) >= self.min_chunk_size:
                start_pos = content.find(current_chunk[0])
                end_pos = start_pos + len(chunk_text)
                
                metadata = self._create_chunk_metadata(
                    chunk_text, chunk_index, start_pos, end_pos, source_file, "paragraph_based"
                )
                
                chunks.append(DocumentChunk(text=chunk_text, metadata=metadata))
        
        return chunks

    def _chunk_semantic_based(self, content: str, source_file: str) -> List[DocumentChunk]:
        """Create chunks based on semantic coherence (simplified version)."""
        # For now, use sentence-based chunking with semantic hints
        # In a full implementation, this would use embeddings to group semantically similar content
        sentences = self._split_into_sentences(content)

        # Group sentences by semantic similarity (simplified heuristic)
        chunks = []
        current_group = []
        current_size = 0
        chunk_index = 0

        for i, sentence in enumerate(sentences):
            sentence_size = len(sentence)

            # Simple semantic grouping: look for topic changes
            is_topic_change = self._detect_topic_change(sentence, current_group)

            if (current_size + sentence_size > self.chunk_size or is_topic_change) and current_group:
                chunk_text = ' '.join(current_group).strip()
                if len(chunk_text) >= self.min_chunk_size:
                    start_pos = content.find(current_group[0])
                    end_pos = start_pos + len(chunk_text)

                    metadata = self._create_chunk_metadata(
                        chunk_text, chunk_index, start_pos, end_pos, source_file, "semantic_based"
                    )
                    metadata.semantic_topic = self._extract_topic_hint(current_group)

                    chunks.append(DocumentChunk(text=chunk_text, metadata=metadata))
                    chunk_index += 1

                current_group = [sentence]
                current_size = sentence_size
            else:
                current_group.append(sentence)
                current_size += sentence_size

        # Handle remaining sentences
        if current_group:
            chunk_text = ' '.join(current_group).strip()
            if len(chunk_text) >= self.min_chunk_size:
                start_pos = content.find(current_group[0])
                end_pos = start_pos + len(chunk_text)

                metadata = self._create_chunk_metadata(
                    chunk_text, chunk_index, start_pos, end_pos, source_file, "semantic_based"
                )
                metadata.semantic_topic = self._extract_topic_hint(current_group)

                chunks.append(DocumentChunk(text=chunk_text, metadata=metadata))

        return chunks

    def _chunk_hybrid(self, content: str, source_file: str) -> List[DocumentChunk]:
        """Create chunks using a hybrid approach combining multiple strategies."""
        # Start with paragraph-based chunking for natural boundaries
        paragraphs = self.paragraph_pattern.split(content)
        paragraphs = [p.strip() for p in paragraphs if p.strip()]

        chunks = []
        chunk_index = 0

        for paragraph in paragraphs:
            if len(paragraph) <= self.max_chunk_size:
                # Paragraph fits in one chunk
                if len(paragraph) >= self.min_chunk_size:
                    start_pos = content.find(paragraph)
                    end_pos = start_pos + len(paragraph)

                    metadata = self._create_chunk_metadata(
                        paragraph, chunk_index, start_pos, end_pos, source_file, "hybrid"
                    )

                    chunks.append(DocumentChunk(text=paragraph, metadata=metadata))
                    chunk_index += 1
            else:
                # Paragraph is too large, use sentence-based chunking
                para_chunks = self._chunk_sentence_based(paragraph, source_file)
                for chunk in para_chunks:
                    chunk.metadata.chunk_index = chunk_index
                    chunk.metadata.strategy_used = "hybrid"
                    chunks.append(chunk)
                    chunk_index += 1

        return chunks

    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        sentences = self.sentence_pattern.split(text)
        # Clean up and filter empty sentences
        sentences = [s.strip() for s in sentences if s.strip()]
        return sentences

    def _find_sentence_boundary(self, content: str, position: int, backward: bool = True) -> int:
        """Find the nearest sentence boundary to the given position."""
        if backward:
            # Look backward for sentence end
            for i in range(position, max(0, position - 200), -1):
                if content[i:i+1] in '.!?':
                    # Check if this is actually end of sentence (not abbreviation)
                    if i + 1 < len(content) and content[i+1].isspace():
                        return i + 1
            return position
        else:
            # Look forward for sentence end
            for i in range(position, min(len(content), position + 200)):
                if content[i:i+1] in '.!?':
                    if i + 1 < len(content) and content[i+1].isspace():
                        return i + 1
            return position

    def _get_overlap_sentences(self, sentences: List[str]) -> List[str]:
        """Get sentences for overlap based on overlap size."""
        if not sentences:
            return []

        overlap_chars = 0
        overlap_sentences = []

        # Take sentences from the end until we reach overlap size
        for sentence in reversed(sentences):
            if overlap_chars + len(sentence) <= self.chunk_overlap:
                overlap_sentences.insert(0, sentence)
                overlap_chars += len(sentence)
            else:
                break

        return overlap_sentences

    def _detect_topic_change(self, sentence: str, current_group: List[str]) -> bool:
        """Detect if a sentence represents a topic change (simplified heuristic)."""
        if not current_group:
            return False

        # Simple heuristics for topic change detection
        topic_indicators = [
            "however", "meanwhile", "furthermore", "in addition", "on the other hand",
            "in contrast", "similarly", "moreover", "nevertheless", "consequently"
        ]

        sentence_lower = sentence.lower()
        return any(indicator in sentence_lower for indicator in topic_indicators)

    def _extract_topic_hint(self, sentences: List[str]) -> str:
        """Extract a topic hint from a group of sentences."""
        if not sentences:
            return "unknown"

        # Simple approach: use first few words of the first sentence
        first_sentence = sentences[0]
        words = self.word_pattern.findall(first_sentence.lower())
        return ' '.join(words[:3]) if words else "unknown"

    def _create_chunk_metadata(
        self,
        chunk_text: str,
        chunk_index: int,
        start_pos: int,
        end_pos: int,
        source_file: str,
        strategy: str
    ) -> ChunkMetadata:
        """Create metadata for a chunk."""
        # Count words and sentences
        words = self.word_pattern.findall(chunk_text)
        sentences = self._split_into_sentences(chunk_text)

        # Generate chunk ID
        chunk_id = f"{source_file}_{chunk_index}_{hashlib.md5(chunk_text.encode()).hexdigest()[:8]}"

        return ChunkMetadata(
            chunk_id=chunk_id,
            chunk_index=chunk_index,
            start_position=start_pos,
            end_position=end_pos,
            word_count=len(words),
            sentence_count=len(sentences),
            source_file=source_file,
            strategy_used=strategy
        )


def create_chunking_engine(
    strategy: str = "hybrid",
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    **kwargs
) -> ChunkingEngine:
    """
    Create a chunking engine with the specified configuration.

    Args:
        strategy: Chunking strategy ("fixed_size", "sentence_based", "paragraph_based", "semantic_based", "hybrid")
        chunk_size: Target chunk size in characters
        chunk_overlap: Overlap between chunks in characters
        **kwargs: Additional configuration options

    Returns:
        Configured ChunkingEngine instance
    """
    strategy_enum = ChunkingStrategy(strategy)

    return ChunkingEngine(
        strategy=strategy_enum,
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        **kwargs
    )
