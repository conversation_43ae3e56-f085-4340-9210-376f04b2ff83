"""
Example: Organizational Integration with Enterprise KG

This example demonstrates how to use the Enterprise KG system in both:
1. Standalone mode (existing behavior)
2. Organizational integration mode (new feature)

The organizational integration allows linking extracted knowledge to existing
file nodes in an organizational graph structure.
"""

from org_integration import create_org_kg_processor


def example_standalone_mode():
    """
    Example of using Enterprise KG in standalone mode (existing behavior).
    This creates a separate knowledge graph without organizational context.
    """
    print("=== STANDALONE MODE (Existing Behavior) ===")
    
    # Create processor in standalone mode
    processor = create_org_kg_processor(
        enable_org_integration=False  # Disable organizational features
    )
    
    # Sample document content
    content = """
    <PERSON> is the Engineering Manager at TechCorp. He manages the Development Team
    and is responsible for the CRM Project. <PERSON> works for the Marketing Department
    and collaborates with <PERSON> on the Product Launch Initiative.
    """
    
    # Process document (existing flow)
    relationships = processor.process_document(
        content=content,
        source_document="org_structure.pdf"
    )
    
    print(f"Extracted {len(relationships)} relationships:")
    for rel in relationships:
        print(f"  {rel.subject} -> {rel.predicate} -> {rel.object}")
    
    print("\nResult: Separate knowledge graph created")
    print("Graph structure:")
    print("  (:Person {name: '<PERSON>'}) -[:MANAGES]-> (:Team {name: 'Development Team'})")
    print("  (:Person {name: 'John Smith'}) -[:RESPONSIBLE_FOR]-> (:Project {name: 'CRM Project'})")
    print("  (:Person {name: 'Sarah Johnson'}) -[:WORKS_FOR]-> (:Department {name: 'Marketing Department'})")


def example_organizational_integration():
    """
    Example of using Enterprise KG with organizational integration.
    This links extracted knowledge to existing organizational file nodes.
    """
    print("\n=== ORGANIZATIONAL INTEGRATION MODE (New Feature) ===")
    
    # Create processor with organizational integration enabled
    processor = create_org_kg_processor(
        enable_org_integration=True  # Enable organizational features
    )
    
    # Sample document content (same as above)
    content = """
    John Smith is the Engineering Manager at TechCorp. He manages the Development Team
    and is responsible for the CRM Project. Sarah Johnson works for the Marketing Department
    and collaborates with John on the Product Launch Initiative.
    """
    
    # Organizational context (simulating existing organizational graph)
    org_context = {
        "org_id": "techcorp",
        "dept_id": "engineering", 
        "team_id": "dev_team"
    }
    
    # Process document with organizational context
    relationships = processor.process_document_with_org_context(
        content=content,
        source_file_id="file_org_chart_123",  # Link to existing file node
        org_context=org_context,
        source_document="org_structure.pdf"
    )
    
    print(f"Extracted {len(relationships)} relationships with organizational context:")
    for rel in relationships:
        print(f"  {rel.subject} -> {rel.predicate} -> {rel.object}")
        print(f"    Context: {rel.org_context}")
        print(f"    Source File: {rel.source_file_id}")
    
    print("\nResult: Integrated knowledge graph")
    print("Graph structure:")
    print("  Existing: (:Organization) -> (:Department) -> (:Team) -> (:File {node_id: 'file_org_chart_123'})")
    print("  New:      (:File) -[:CONTAINS]-> (:Person {name: 'John Smith', org_id: 'techcorp'})")
    print("  New:      (:File) -[:CONTAINS]-> (:Team {name: 'Development Team', org_id: 'techcorp'})")
    print("  New:      (:Person) -[:MANAGES]-> (:Team)")


def example_context_aware_queries():
    """
    Example of context-aware queries enabled by organizational integration.
    """
    print("\n=== CONTEXT-AWARE QUERIES ===")
    
    processor = create_org_kg_processor(enable_org_integration=True)
    
    # Query entities within specific organizational context
    print("1. Find all people in Engineering department:")
    engineering_people = processor.query_by_organizational_context(
        org_id="techcorp",
        dept_id="engineering",
        entity_type="Person"
    )
    
    for result in engineering_people:
        entity = result["entity"]
        source_file = result["source_file"]
        print(f"   {entity['name']} (from {source_file})")
    
    print("\n2. Find all projects in TechCorp:")
    techcorp_projects = processor.query_by_organizational_context(
        org_id="techcorp",
        entity_type="Project"
    )
    
    for result in techcorp_projects:
        entity = result["entity"]
        print(f"   {entity['name']} (Department: {entity.get('department_id', 'N/A')})")


def example_file_knowledge_discovery():
    """
    Example of discovering all knowledge extracted from a specific file.
    """
    print("\n=== FILE KNOWLEDGE DISCOVERY ===")
    
    processor = create_org_kg_processor(enable_org_integration=True)
    
    # Find all knowledge extracted from a specific file
    file_knowledge = processor.find_file_knowledge("file_org_chart_123")
    
    print(f"Knowledge extracted from file {file_knowledge['file_id']}:")
    
    print(f"\nEntities ({len(file_knowledge['entities'])}):")
    for entity_info in file_knowledge['entities']:
        entity = entity_info['entity']
        print(f"  {entity['name']} ({entity_info['type']})")
    
    print(f"\nRelationships ({len(file_knowledge['relationships'])}):")
    for rel_info in file_knowledge['relationships']:
        source = rel_info['source']
        target = rel_info['target']
        rel_type = rel_info['type']
        print(f"  {source['name']} -[{rel_type}]-> {target['name']}")


def example_backward_compatibility():
    """
    Example showing that organizational integration is fully optional.
    """
    print("\n=== BACKWARD COMPATIBILITY ===")
    
    # Create processor with org integration enabled but don't use org features
    processor = create_org_kg_processor(enable_org_integration=True)
    
    content = "Alice leads the Data Science team."
    
    # Use basic processing method - should work exactly like standalone mode
    relationships = processor.process_document(content)
    
    print("Using org-enabled processor in standalone mode:")
    print(f"Extracted: {relationships[0].subject} -> {relationships[0].predicate} -> {relationships[0].object}")
    print("Result: No organizational context added, works exactly like before")


if __name__ == "__main__":
    """
    Run all examples to demonstrate the optional organizational integration.
    """
    
    print("Enterprise KG Organizational Integration Examples")
    print("=" * 50)
    
    # Example 1: Standalone mode (existing behavior)
    example_standalone_mode()
    
    # Example 2: Organizational integration (new feature)
    example_organizational_integration()
    
    # Example 3: Context-aware queries
    example_context_aware_queries()
    
    # Example 4: File knowledge discovery
    example_file_knowledge_discovery()
    
    # Example 5: Backward compatibility
    example_backward_compatibility()
    
    print("\n" + "=" * 50)
    print("Summary:")
    print("✅ Standalone mode: Works exactly as before")
    print("✅ Organizational integration: Optional new feature")
    print("✅ Backward compatibility: Existing code unchanged")
    print("✅ Context-aware search: Enabled when using org integration")
    print("✅ File linking: Connects extracted knowledge to source files")
