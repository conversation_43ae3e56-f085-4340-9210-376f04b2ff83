# Enhanced Node Properties for GraphRAG

## Overview

The enterprise KG system now automatically adds rich, contextual properties to nodes based on their entity types. These properties significantly enhance GraphRAG (Graph Retrieval-Augmented Generation) capabilities by providing semantic context, importance scoring, and relationship hints.

## What's New

### ✅ **Automatic Property Enhancement**
- Every node gets enhanced properties based on its entity type
- Properties are sourced from comprehensive constants
- No manual configuration required

### ✅ **GraphRAG-Optimized Properties**
- **Graph Importance**: Scoring for result prioritization (0.0-1.0)
- **Category**: High-level grouping (People, Organizations, Technology, etc.)
- **Description**: Human-readable entity description
- **Context Keywords**: Semantic search terms
- **Typical Relationships**: Expected relationship patterns
- **Type-Specific Flags**: Boolean properties for filtering

## Enhanced Properties by Entity Type

### 👥 **People Entities**
```json
{
  "is_human": true,
  "can_have_relationships": true,
  "leadership_role": true,  // for managers/executives
  "seniority_level": "executive",  // for executives
  "employment_status": "active",  // for employees
  "typical_relationships": ["works_for", "manages", "reports_to"],
  "context_keywords": ["employee", "manager", "leader"],
  "graph_importance": 0.9-1.0
}
```

### 🏢 **Organization Entities**
```json
{
  "is_organization": true,
  "can_employ_people": true,
  "is_internal_unit": true,  // for departments/teams
  "collaborative_unit": true,  // for teams
  "typical_relationships": ["employs", "contains", "part_of"],
  "context_keywords": ["company", "department", "team"],
  "graph_importance": 0.8-0.95
}
```

### 💻 **Technology Entities**
```json
{
  "is_technology": true,
  "can_be_integrated": true,
  "is_software": true,  // for applications
  "stores_data": true,  // for databases
  "typical_relationships": ["integrates_with", "runs_on", "accesses"],
  "context_keywords": ["system", "software", "platform"],
  "graph_importance": 0.75-0.8
}
```

### 📋 **Project Entities**
```json
{
  "is_initiative": true,
  "has_timeline": true,
  "strategic_importance": "high",  // for initiatives
  "typical_relationships": ["involves", "managed_by", "funded_by"],
  "context_keywords": ["project", "initiative", "program"],
  "graph_importance": 0.9
}
```

### 📚 **Document Entities**
```json
{
  "is_information": true,
  "contains_knowledge": true,
  "formal_document": true,  // for reports
  "typical_relationships": ["authored_by", "mentions", "references"],
  "context_keywords": ["document", "report", "file"],
  "graph_importance": 0.7
}
```

## GraphRAG Benefits

### 🎯 **1. Intelligent Result Prioritization**
```cypher
// Find most important entities for comprehensive answers
MATCH (n)
WHERE n.graph_importance > 0.8
RETURN n.name, n.entity_type, n.graph_importance
ORDER BY n.graph_importance DESC
```

**Benefit**: GraphRAG can prioritize central entities (executives, key projects, major systems) in responses.

### 🔍 **2. Semantic Search Enhancement**
```cypher
// Search using context keywords
MATCH (n)
WHERE any(keyword IN n.context_keywords 
          WHERE keyword CONTAINS $search_term)
RETURN n.name, n.entity_type, n.description
```

**Benefit**: Better matching of user queries to relevant entities through semantic keywords.

### 🏗️ **3. Structured Knowledge Organization**
```cypher
// Organize knowledge by categories
MATCH (n)
RETURN n.category, 
       collect(n.name) as entities,
       avg(n.graph_importance) as avg_importance
ORDER BY avg_importance DESC
```

**Benefit**: GraphRAG can provide organized, categorized responses.

### 🔗 **4. Relationship Pattern Discovery**
```cypher
// Find entities with specific relationship capabilities
MATCH (leader)
WHERE leader.leadership_role = true
MATCH (leader)-[r:MANAGES|LEADS]->(subordinate)
RETURN leader.name, type(r), subordinate.name
```

**Benefit**: Quickly identify organizational hierarchies and management structures.

### 🎛️ **5. Type-Specific Filtering**
```cypher
// Find integration opportunities
MATCH (tech)
WHERE tech.is_technology = true 
  AND tech.can_be_integrated = true
MATCH (tech)-[r]-(other)
RETURN tech.name, other.name, type(r)
```

**Benefit**: Target specific types of entities for specialized queries.

## Implementation Details

### **Automatic Enhancement**
Properties are automatically added during node creation in `Neo4jClient.create_entity_relationship()`:

```python
# Get enhanced properties for both entities
source_properties = self._get_enhanced_entity_properties(entity_rel.subject_type)
target_properties = self._get_enhanced_entity_properties(entity_rel.object_type)

# Apply properties during node creation
SET source += $source_properties
SET target += $target_properties
```

### **Property Sources**
Properties come from `constants/entities.py`:
- `get_entity_properties(entity_type)` - Main function
- `_get_entity_category(entity_type)` - Category mapping
- `_get_graph_importance(entity_type)` - Importance scoring

### **Fallback Handling**
If constants are unavailable, fallback properties are provided:
```python
{
    "description": f"A {entity_type.lower()} entity",
    "category": "General", 
    "searchable": True,
    "graph_importance": 0.5
}
```

## GraphRAG Query Examples

### **Find Key Decision Makers**
```cypher
MATCH (leader)
WHERE leader.leadership_role = true 
  AND leader.graph_importance > 0.9
RETURN leader.name, leader.entity_type, leader.seniority_level
ORDER BY leader.graph_importance DESC
```

### **Discover Knowledge Sources**
```cypher
MATCH (doc)
WHERE doc.contains_knowledge = true
MATCH (doc)-[:MENTIONS]->(entity)
RETURN doc.name, doc.category, collect(entity.name) as mentioned_entities
```

### **Map Technology Landscape**
```cypher
MATCH (tech)
WHERE tech.is_technology = true
OPTIONAL MATCH (tech)-[r:INTEGRATES_WITH]-(other)
RETURN tech.name, tech.description, 
       collect(other.name) as integrations
```

### **Analyze Organizational Structure**
```cypher
MATCH (org)
WHERE org.is_organization = true
OPTIONAL MATCH (org)-[:CONTAINS]->(unit)
WHERE unit.is_internal_unit = true
RETURN org.name, collect(unit.name) as internal_units
```

## Usage in Production

### **1. Process Documents**
```bash
# Enhanced properties are automatically added
python main.py --documents /path/to/docs
```

### **2. Query Enhanced Graph**
```python
# Use Neo4j client to query enhanced properties
results = neo4j_client.query_entities(entity_type="Manager")
for entity in results:
    if entity.get('leadership_role'):
        print(f"Leader: {entity['name']}")
```

### **3. GraphRAG Integration**
Enhanced properties provide rich context for:
- **Entity ranking** by importance scores
- **Semantic matching** via context keywords  
- **Structured responses** using categories
- **Relationship discovery** through type flags

## Benefits Summary

✅ **Richer Context**: Semantic properties enhance entity understanding  
✅ **Better Ranking**: Importance scores prioritize key entities  
✅ **Organized Knowledge**: Categories structure information  
✅ **Semantic Search**: Keywords improve query matching  
✅ **Relationship Hints**: Type flags guide graph traversal  
✅ **Zero Configuration**: Properties added automatically  

The enhanced properties transform a basic knowledge graph into a semantically rich, GraphRAG-optimized information system that provides more intelligent and contextual responses.
