# Proposed Changes Plan for EKG Graphiti Updates

This document outlines the plan to implement the requested changes for the Enterprise Knowledge Graph (EKG) with Graphiti project.

**User Requirements:**

1.  Remove the part where episode embeddings are added to Neo4j.
2.  Provide clear output of each chunk with text, ID, and other details.
3.  Remove unnecessary logs.
4.  Ensure same entities are merged in Neo4j (case-insensitive name, exact type).

**Overall Strategy:**
Prioritize modifications within the `enterprise_kg_graphiti` project. Changes to `graphiti-core` (like direct Python code or Cypher query modifications) will be avoided if possible, favoring prompt engineering or adjustments in `GraphitiEnterpriseProcessor`. If `graphiti-core` prompt changes are insufficient for entity merging, a post-processing step in `GraphitiEnterpriseProcessor` will be implemented.

---

## Phase 1: Initial Setup & Analysis (Completed)

*   **Goal:** Confirm understanding and establish a baseline.
*   **Actions Taken:**
    *   Reviewed PRD and user requirements.
    *   Asked clarifying questions and received answers.
    *   Analyzed relevant files:
        *   `ekg_Graphiti_updated/PROJECT_PRD.md`
        *   `enterprise_kg_graphiti/core/processor.py` (`GraphitiEnterpriseProcessor`, `_add_chunk_as_episode`)
        *   `graphiti/graphiti_core/graphiti.py` (`Graphiti.add_episode`)
        *   `graphiti/graphiti_core/utils/bulk_utils.py` (`add_nodes_and_edges_bulk_tx`, `node_name_match`)
        *   `graphiti/graphiti_core/nodes.py` (`EpisodicNode`, `EntityNode` class definitions)
        *   `graphiti/graphiti_core/models/nodes/node_db_queries.py` (`EPISODIC_NODE_SAVE_BULK`, `ENTITY_NODE_SAVE_BULK`)
        *   `graphiti/graphiti_core/utils/maintenance/node_operations.py` (`resolve_extracted_nodes`, `extract_nodes`)
        *   `graphiti/graphiti_core/prompts/dedupe_nodes.py` (the `nodes` prompt function)
    *   Formulated this detailed plan.

---

## Phase 2: Implement Changes

### 1. Requirement: Remove Episode Embeddings from Neo4j

*   **Goal:** Prevent `EpisodicNode` embeddings from being generated or passed to Neo4j.
*   **Analysis & Conclusion:**
    *   The `EPISODIC_NODE_SAVE_BULK` Cypher query in `graphiti-core` does *not* explicitly save an `embedding` field for `EpisodicNode`s.
    *   The `EpisodicNode` class in `graphiti-core` does not define an `embedding` field.
    *   Therefore, `graphiti-core` itself is unlikely to be storing these embeddings unless an `embedding` attribute is already present on the `EpisodicNode` object when it's processed by the `SET n = episode` part of the bulk save query.
*   **Action:**
    *   The primary action is to ensure that no `embedding` property is being set on `EpisodicNode` objects *within the `enterprise_kg_graphiti` codebase* before they are passed to `graphiti-core`.
    *   If the `SimpleEmbedder` (used by default in `GraphitiEnterpriseProcessor` via `create_simple_embedder` in `enterprise_kg_graphiti/core/simple_embedder.py`) is found to be adding an `embedding` attribute directly to the `EpisodicNode` objects it processes, modifications will be made to `GraphitiEnterpriseProcessor` or `SimpleEmbedder` to prevent this, or to remove such an attribute before saving.
    *   **Target files for potential modification:**
        *   `enterprise_kg_graphiti/core/processor.py` (to intercept/modify `EpisodicNode` objects or how the embedder is used for them).
        *   `enterprise_kg_graphiti/core/simple_embedder.py` (if it's directly mutating node objects with embeddings).

### 2. Requirement: Clear output of each chunk

*   **Goal:** Log chunk text, `EpisodicNode` UUID, source filename, and chunk number to `enterprise_kg_graphiti/logs/chunk_details.log`.
*   **Action:**
    *   In `enterprise_kg_graphiti/core/processor.py`, within the `_add_chunk_as_episode` method:
        *   After `episode_result = await self.graphiti.add_episode(...)`, extract:
            *   Chunk text: `chunk.text`
            *   EpisodicNode UUID: `episode_result.episode.uuid`
            *   Source filename: `document.source_file`
            *   Chunk number: `chunk.chunk_index + 1`
        *   Implement a new Python logger (e.g., `chunk_detail_logger`).
        *   Configure this logger to write to `enterprise_kg_graphiti/logs/chunk_details.log`. The `logs` directory will be created if it doesn't exist.
        *   Log the extracted information in a clear, structured format.
    *   **Target file for modification:** `enterprise_kg_graphiti/core/processor.py`.

### 3. Requirement: Remove unnecessary logs

*   **Goal:** Reduce verbose logging in `enterprise_kg_graphiti`.
*   **Action:**
    *   Review `enterprise_kg_graphiti/core/processor.py` for `logger.debug` statements that are too verbose for standard operation (e.g., printing full chunk text previews, excessive per-chunk status updates).
    *   Comment out these logs or change their level to a less verbose one (e.g., a custom `TRACE` level if implemented, or remove if not valuable even for deep debugging).
    *   **Target file for modification:** `enterprise_kg_graphiti/core/processor.py`.

### 4. Requirement: Merge same entities in Neo4j

*   **Goal:** Ensure entities with the same name (case-insensitive) and same type are merged.
*   **Strategy:**
    1.  **Attempt Prompt Engineering (within `graphiti-core` - if allowed by user during implementation):**
        *   Modify the `nodes` prompt in `graphiti/graphiti_core/prompts/dedupe_nodes.py`.
        *   Add specific instructions for case-insensitive name matching.
        *   Add specific instructions for exact type matching (based on the `entity_type` list).
    2.  **Implement Post-processing (in `enterprise_kg_graphiti` - fallback or primary if core changes are disallowed):**
        *   In `enterprise_kg_graphiti/core/processor.py`, within `_add_chunk_as_episode`, after `self.graphiti.add_episode(...)` returns `episode_result.nodes`:
            *   For each entity node in `episode_result.nodes`:
                *   Query Neo4j directly: `MATCH (e:Entity) WHERE toLower(e.name) = toLower($name) AND e.labels = $labels RETURN e.uuid, e.name, e.labels, e.created_at ORDER BY e.created_at ASC`.
                *   If multiple nodes are returned (indicating `graphiti-core` didn't fully merge them per the new criteria):
                    *   Identify the canonical node (e.g., the oldest one).
                    *   Use Cypher (e.g., `apoc.refactor.mergeNodes` or manual relationship transfer and deletion) to merge the duplicates into the canonical node. This involves:
                        *   Moving all incoming and outgoing relationships from duplicate nodes to the canonical node.
                        *   Deleting the duplicate nodes.
    *   **Target files for modification:**
        *   Potentially `graphiti/graphiti_core/prompts/dedupe_nodes.py` (if prompt engineering is pursued and allowed).
        *   `enterprise_kg_graphiti/core/processor.py` (for post-processing logic).

**Mermaid Diagram for Entity Deduplication (Post-Processing Approach):**

```mermaid
graph TD
    A[Document Processing in GraphitiEnterpriseProcessor] --> B(Raw Chunks)
    B --> C{Graphiti Core: graphiti.add_episode}
    C --> D[Initial Entity Extraction & Deduplication by Graphiti Core LLM]
    D --> E{Entities Returned to GraphitiEnterpriseProcessor}
    E --> F{Post-Processing in GraphitiEnterpriseProcessor}
    F -- Query by lower(name) AND exact labels --> G[Neo4j]
    G -- Multiple matches? --> H{Yes: Manual Merge Logic}
    H -- Cypher: Merge Nodes, Move Rels, Delete Duplicates --> G
    F -- Single match or no action needed --> I[Process Complete for Entity]
    G -- No/Single Match --> I

    style C fill:#ccf,stroke:#333,stroke-width:2px
    style F fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#f9f,stroke:#333,stroke-width:2px
```

---

## Phase 3: Testing and Iteration

*   **Goal:** Verify changes and ensure system stability.
*   **Actions:**
    *   Prepare diverse sample documents to test all changes, especially entity merging scenarios (e.g., same name different case, same name same type, same name different type).
    *   Execute processing and meticulously review `enterprise_kg_graphiti/logs/chunk_details.log` for correctness and completeness.
    *   Inspect the Neo4j database to:
        *   Confirm that `EpisodicNode`s do not have an `embedding` property.
        *   Verify that entities are correctly merged according to the case-insensitive name and exact type criteria.
        *   Ensure no data loss or incorrect relationship transfers during merges.
    *   Monitor general application logs for clarity and ensure unnecessary verbose logs have been removed.
    *   Iterate on prompt engineering or post-processing logic as needed based on test results.

---
This plan will be implemented by switching to Code mode.