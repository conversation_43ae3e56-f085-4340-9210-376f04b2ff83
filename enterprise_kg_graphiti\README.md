# Enterprise Knowledge Graph with Graphiti

A powerful enterprise-ready knowledge graph implementation that leverages Graphiti's capabilities while maintaining compliance with defined schema ontologies.

## Features

- **Schema-Aware Processing**: Uses your existing enterprise entity and relationship type definitions
- **Multi-Format Support**: Processes PDF, DOCX, TXT, JSON, and Markdown files
- **Intelligent Chunking**: Efficiently handles large documents with configurable chunk sizes
- **Enterprise Search**: Advanced search capabilities with schema-aware filtering
- **Graphiti Integration**: Leverages Graphiti's state-of-the-art knowledge graph technology
- **Scalable Architecture**: Designed for enterprise-scale document processing

## Quick Start

### 1. Installation

```bash
# Clone or copy the enterprise_kg_graphiti folder to your project
cd enterprise_kg_graphiti

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Setup

Create a `.env` file with your configuration:

```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# OpenAI Configuration (for LLM and embeddings)
OPENAI_API_KEY=your_openai_api_key

# Optional: Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
GROUP_ID=enterprise_kg
```

### 3. Basic Usage

```python
import asyncio
from enterprise_kg_graphiti import GraphitiEnterpriseProcessor

async def main():
    # Initialize processor
    processor = GraphitiEnterpriseProcessor(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="your_password"
    )
    
    # Initialize the knowledge graph
    await processor.initialize()
    
    # Process documents
    results = await processor.process_directory("./documents")
    
    # Search the knowledge graph
    search_results = await processor.search("project management")
    
    # Clean up
    await processor.close()

asyncio.run(main())
```

### 4. Command Line Usage

```bash
# Process documents in a directory
python main.py --documents ./documents

# Process a single file
python main.py --file document.pdf

# Create sample documents and process them
python main.py --create-samples --documents ./sample_docs

# Search after processing
python main.py --documents ./docs --search "team members"

# Check configuration
python main.py --config-check

# Show schema information
python main.py --schema-info
```

## Architecture

### Core Components

1. **GraphitiEnterpriseProcessor**: Main processor that orchestrates document processing and knowledge graph creation
2. **SchemaAdapter**: Bridges enterprise schema definitions with Graphiti's format
3. **DocumentProcessor**: Handles document parsing, chunking, and preprocessing
4. **SearchInterface**: Provides enterprise-focused search capabilities

### Schema Integration

The system uses your existing enterprise schema definitions from `enterprise_kg_minimal/constants/`:

- **Entity Types**: Person, Project, Company, Document, etc.
- **Relationship Types**: works_for, manages, involved_in, etc.
- **Properties**: Rich metadata for enhanced GraphRAG context

### Document Processing Flow

1. **Document Ingestion**: Supports PDF, DOCX, TXT, JSON, MD files
2. **Intelligent Chunking**: Splits large documents while preserving context
3. **Schema-Aware Extraction**: Uses enterprise ontology for entity/relationship extraction
4. **Knowledge Graph Storage**: Stores in Neo4j via Graphiti
5. **Search Indexing**: Creates searchable embeddings and indices

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEO4J_URI` | Neo4j database URI | `bolt://localhost:7687` |
| `NEO4J_USER` | Neo4j username | `neo4j` |
| `NEO4J_PASSWORD` | Neo4j password | `password` |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `CHUNK_SIZE` | Document chunk size | `1000` |
| `CHUNK_OVERLAP` | Chunk overlap size | `200` |
| `GROUP_ID` | Episode group ID | `enterprise_kg` |

### Processing Configuration

```python
from enterprise_kg_graphiti.config import EnterpriseKGConfig

config = EnterpriseKGConfig()
config.processing.chunk_size = 1500
config.processing.chunk_overlap = 300
config.processing.supported_extensions = ['.pdf', '.docx', '.txt']
```

## Examples

### Basic Document Processing

```python
import asyncio
from enterprise_kg_graphiti import GraphitiEnterpriseProcessor

async def process_documents():
    processor = GraphitiEnterpriseProcessor(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j", 
        neo4j_password="password"
    )
    
    await processor.initialize()
    
    # Process all documents in a directory
    results = await processor.process_directory(
        directory_path="./documents",
        file_patterns=[".pdf", ".docx", ".txt"]
    )
    
    print(f"Processed {len(results)} documents")
    await processor.close()

asyncio.run(process_documents())
```

### Advanced Search

```python
# Search with entity type filtering
results = await processor.search(
    query="project management",
    entity_types=["Person", "Project"],
    limit=10
)

# Search for specific relationship types
relationships = await processor.search(
    query="team collaboration",
    relationship_types=["works_for", "manages", "collaborates_with"],
    limit=15
)

# Get all relationships for an entity
entity_rels = await processor.get_entity_relationships("John Smith")
```

### Schema Exploration

```python
# Get schema summary
schema = await processor.get_schema_summary()
print(f"Entity types: {schema['entity_types']}")
print(f"Relationship types: {schema['relationship_types']}")

# Get processing statistics
stats = processor.get_processing_stats()
print(f"Chunk size: {stats['chunk_size']}")
```

## Supported File Types

- **Text Files**: `.txt`, `.md`
- **Documents**: `.docx` (requires python-docx)
- **PDFs**: `.pdf` (requires PyPDF2)
- **Structured Data**: `.json`

## Enterprise Features

### Schema Compliance
- Uses your existing entity and relationship type definitions
- Validates extracted entities against enterprise ontology
- Maintains consistency with existing knowledge graphs

### Scalable Processing
- Configurable chunk sizes for optimal performance
- Batch processing capabilities
- Memory-efficient document handling

### Advanced Search
- Hybrid search combining semantic similarity and keyword matching
- Schema-aware filtering by entity and relationship types
- Graph-based result ranking

### Integration Ready
- Compatible with existing enterprise systems
- RESTful API potential for web applications
- Extensible architecture for custom requirements

## Comparison with enterprise_kg_minimal

| Feature | enterprise_kg_minimal | enterprise_kg_graphiti |
|---------|----------------------|----------------------|
| Knowledge Graph Engine | Custom Neo4j integration | Graphiti (state-of-the-art) |
| Entity Extraction | Custom LLM prompts | Graphiti's optimized extraction |
| Search Capabilities | Basic Neo4j queries | Advanced hybrid search |
| Document Processing | Basic chunking | Intelligent chunking |
| Schema Integration | Direct implementation | Adapter pattern |
| Scalability | Manual optimization | Built-in optimization |
| Community Detection | Manual | Automatic via Graphiti |
| Temporal Relationships | Basic | Advanced temporal modeling |

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black enterprise_kg_graphiti/
isort enterprise_kg_graphiti/
```

### Type Checking

```bash
mypy enterprise_kg_graphiti/
```

## Troubleshooting

### Common Issues

1. **Neo4j Connection Failed**
   - Ensure Neo4j is running
   - Check URI, username, and password
   - Verify network connectivity

2. **OpenAI API Errors**
   - Verify API key is set correctly
   - Check API quota and billing
   - Ensure model availability

3. **Document Processing Errors**
   - Check file permissions
   - Verify file format support
   - Review chunk size settings

4. **Memory Issues**
   - Reduce chunk size
   - Process files individually
   - Increase system memory

### Logging

Enable debug logging for detailed information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Use type hints
5. Follow PEP 8 style guidelines

## License

This project follows the same license as the parent enterprise_kg_minimal project.
