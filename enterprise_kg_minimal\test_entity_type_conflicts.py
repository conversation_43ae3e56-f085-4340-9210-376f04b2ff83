#!/usr/bin/env python3
"""
Test Entity Type Conflicts in Enterprise KG

This script tests what happens when the same entity name appears 
with different entity types across documents.
"""

import os
from dotenv import load_dotenv
from constants.schemas import EntityRelationship
from storage.neo4j_client import Neo4j<PERSON><PERSON>, Neo4jConnection

# Load environment variables
load_dotenv()

def test_entity_type_conflicts():
    """Test behavior when same entity name has different types."""
    
    # Create Neo4j client
    conn = Neo4jConnection(
        uri=os.getenv('NEO4J_URI'),
        user=os.getenv('NEO4J_USER'), 
        password=os.getenv('NEO4J_PASSWORD'),
        database=os.getenv('NEO4J_DATABASE')
    )
    client = Neo4jClient(conn)
    
    print("🧪 Testing Entity Type Conflicts")
    print("=" * 40)
    
    # Clear existing data
    driver = client._get_driver()
    with driver.session(database=client.connection.database) as session:
        session.run('MATCH (n) DETACH DELETE n')
        print("✅ Cleared existing data")
    
    # Test Case: Same name "Alpha" with different entity types
    print("\n📄 Document 1: 'Alpha' as a Project")
    
    relationships_doc1 = [
        EntityRelationship(
            subject="John",
            predicate="manages",
            object="Alpha",
            subject_type="Person",
            object_type="Project",  # Alpha is a Project
            confidence_score=0.9,
            context="Project management",
            source_sentence="John manages Project Alpha"
        )
    ]
    
    results1 = client.batch_create_entity_relationships(relationships_doc1, "doc1.pdf")
    print(f"✅ Stored {len(results1)} relationships")
    
    # Check state after doc1
    with driver.session(database=client.connection.database) as session:
        result = session.run("MATCH (n {name: 'Alpha'}) RETURN labels(n) as labels, n.entity_type as type")
        alpha_info = result.single()
        print(f"📊 Alpha node: {alpha_info['labels']} with entity_type: {alpha_info['type']}")
    
    print("\n📄 Document 2: 'Alpha' as a Company")
    
    relationships_doc2 = [
        EntityRelationship(
            subject="Sarah",
            predicate="works_for",
            object="Alpha",
            subject_type="Person",
            object_type="Company",  # Alpha is a Company
            confidence_score=0.8,
            context="Employment",
            source_sentence="Sarah works for Alpha Company"
        )
    ]
    
    results2 = client.batch_create_entity_relationships(relationships_doc2, "doc2.pdf")
    print(f"✅ Stored {len(results2)} relationships")
    
    # Check final state
    with driver.session(database=client.connection.database) as session:
        result = session.run("MATCH (n {name: 'Alpha'}) RETURN count(n) as alpha_count")
        alpha_count = result.single()['alpha_count']
        print(f"📊 Total 'Alpha' nodes: {alpha_count}")
        
        # Get all Alpha nodes with their labels and types
        result = session.run("""
            MATCH (n {name: 'Alpha'})
            RETURN labels(n) as labels, 
                   n.entity_type as entity_type,
                   n.created_at as created_at,
                   n.updated_at as updated_at
        """)
        
        print(f"\n🔍 Alpha node details:")
        for i, record in enumerate(result, 1):
            print(f"   Node {i}: {record['labels']} (entity_type: {record['entity_type']})")
            print(f"           Created: {record['created_at']}")
            print(f"           Updated: {record['updated_at']}")
        
        # Get all relationships involving Alpha
        result = session.run("""
            MATCH (alpha {name: 'Alpha'})-[r]-(other)
            RETURN type(r) as rel_type,
                   other.name as other_name,
                   labels(other)[0] as other_type,
                   r.source_document as source_doc,
                   labels(alpha)[0] as alpha_label
            ORDER BY source_doc
        """)
        
        print(f"\n🔗 All relationships involving 'Alpha':")
        for record in result:
            alpha_label = record['alpha_label']
            rel_type = record['rel_type']
            other_name = record['other_name']
            other_type = record['other_type']
            source_doc = record['source_doc']
            print(f"   ({alpha_label}) Alpha --{rel_type}--> {other_name} ({other_type}) [from {source_doc}]")
    
    print(f"\n🎯 ANALYSIS:")
    if alpha_count == 1:
        print("   ✅ Same entity name creates single node (merged)")
        print("   ⚠️  Entity type gets overwritten by latest document")
        print("   💡 This could be problematic for conflicting entity types")
    else:
        print("   ❌ Same entity name creates multiple nodes")
        print("   💡 This suggests nodes are differentiated by label")
    
    client.close()
    return alpha_count

if __name__ == "__main__":
    count = test_entity_type_conflicts()
    print(f"\n📊 Result: {count} node(s) created for entity 'Alpha'")
