"""
Schema Adapter for Enterprise Knowledge Graph

This module provides functionality to adapt the existing enterprise schema
definitions to work with Graphiti's knowledge graph system.
"""

import sys
import os
from typing import Dict, List, Set, Optional, Any, Tuple
from collections import defaultdict # Added for new_entity_type_map
from pydantic import BaseModel
from dataclasses import dataclass

# Import the existing schema definitions
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'enterprise_kg_minimal'))

try:
    from constants.entities import EntityType, get_all_entity_types, get_entity_properties
    from constants.relationships import RelationshipType, get_all_relationship_types, get_common_entity_relationship_patterns # Added import
    from constants.schemas import Entity, EntityRelationship
except ImportError as e:
    print(f"Warning: Could not import enterprise schema definitions: {e}")
    print("Please ensure enterprise_kg_minimal is in the correct location")


class GraphitiEntityType(BaseModel):
    """Pydantic model for Graphiti entity types"""
    display_name: str
    description: str
    properties: Dict[str, Any] = {}


class GraphitiRelationshipType(BaseModel):
    """Pydantic model for Graphiti relationship types"""
    display_name: str
    description: str
    properties: Dict[str, Any] = {}


@dataclass
class SchemaMapping:
    """Mapping between enterprise schema and Graphiti schema"""
    entity_types: Dict[str, GraphitiEntityType]
    relationship_types: Dict[str, GraphitiRelationshipType]
    entity_type_map: Dict[Tuple[str, str], List[str]]  # (source_type, target_type) -> [relationship_types]


class SchemaAdapter:
    """
    Adapts enterprise schema definitions to Graphiti format.

    This class bridges the gap between the existing enterprise ontology
    and Graphiti's expected schema format.
    """

    def __init__(self):
        """Initialize the schema adapter."""
        self.schema_mapping = self._create_schema_mapping()

    def _create_schema_mapping(self) -> SchemaMapping:
        """Create mapping between enterprise and Graphiti schemas."""

        # Convert entity types
        entity_types = {}
        try:
            for entity_type in EntityType:
                properties = get_entity_properties(entity_type)
                entity_types[entity_type.value] = GraphitiEntityType(
                    display_name=entity_type.value,
                    description=properties.get('description', f'Entity of type {entity_type.value}'),
                    properties=properties
                )
        except NameError:
            # Fallback if enterprise schema not available
            entity_types = {
                'Entity': GraphitiEntityType(
                    display_name='Entity',
                    description='Generic entity type',
                    properties={}
                )
            }

        # Convert relationship types
        relationship_types = {}
        try:
            for rel_type in RelationshipType:
                relationship_types[rel_type.value] = GraphitiRelationshipType(
                    display_name=rel_type.value,
                    description=f'Relationship of type {rel_type.value}',
                    properties={}
                )
        except NameError:
            # Fallback if enterprise schema not available
            relationship_types = {
                'related_to': GraphitiRelationshipType(
                    display_name='related_to',
                    description='Generic relationship',
                    properties={}
                )
            }

        # Create entity type mapping for relationships
        entity_type_map: Dict[Tuple[str, str], List[str]] = defaultdict(list)
        try:
            common_patterns = get_common_entity_relationship_patterns()
            if common_patterns:
                for subj_type, rel_type_str, obj_type in common_patterns:
                    # Ensure the relationship type is one we've defined/loaded
                    if rel_type_str in relationship_types:
                        # Ensure subject and object types are known, or default to 'Entity' if not strictly typed here
                        # For simplicity, we assume subj_type and obj_type from patterns are valid strings.
                        # Graphiti core will handle actual entity type validation based on what's passed.
                        entity_type_map[(subj_type, obj_type)].append(rel_type_str)
                
                # Deduplicate relationship types for each pair
                for key in list(entity_type_map.keys()): # Iterate over a copy of keys for modification
                    entity_type_map[key] = list(set(entity_type_map[key]))
            
            # If after processing patterns, the map is still empty, or if an error occurred,
            # fall back to the generic map to ensure graphiti has something.
            if not entity_type_map:
                print("Warning: Could not build specific edge_type_map from common_patterns. Falling back to generic map.")
                entity_type_map[('Entity', 'Entity')] = list(relationship_types.keys())

        except NameError: # Handles if get_common_entity_relationship_patterns is not imported
            print("Warning: get_common_entity_relationship_patterns not found. Falling back to generic edge_type_map.")
            entity_type_map[('Entity', 'Entity')] = list(relationship_types.keys())
        except Exception as e_map:
            print(f"Error building edge_type_map: {e_map}. Falling back to generic map.")
            entity_type_map[('Entity', 'Entity')] = list(relationship_types.keys())


        return SchemaMapping(
            entity_types=entity_types,
            relationship_types=relationship_types,
            entity_type_map=dict(entity_type_map) # Convert defaultdict to dict
        )

    def get_entity_types_for_graphiti(self) -> Dict[str, BaseModel]:
        """Get entity types in Graphiti format."""
        return {name: entity_type for name, entity_type in self.schema_mapping.entity_types.items()}

    def get_relationship_types_for_graphiti(self) -> Dict[str, BaseModel]:
        """Get relationship types in Graphiti format."""
        return {name: rel_type for name, rel_type in self.schema_mapping.relationship_types.items()}

    def get_edge_type_map_for_graphiti(self) -> Dict[Tuple[str, str], List[str]]:
        """Get edge type mapping for Graphiti."""
        return self.schema_mapping.entity_type_map

    def validate_entity_type(self, entity_type: str) -> bool:
        """Validate if an entity type is supported."""
        return entity_type in self.schema_mapping.entity_types

    def validate_relationship_type(self, relationship_type: str) -> bool:
        """Validate if a relationship type is supported."""
        return relationship_type in self.schema_mapping.relationship_types

    def get_entity_properties(self, entity_type: str) -> Dict[str, Any]:
        """Get properties for a specific entity type."""
        if entity_type in self.schema_mapping.entity_types:
            return self.schema_mapping.entity_types[entity_type].properties
        return {}

    def get_supported_entity_types(self) -> List[str]:
        """Get list of all supported entity types."""
        return list(self.schema_mapping.entity_types.keys())

    def get_supported_relationship_types(self) -> List[str]:
        """Get list of all supported relationship types."""
        return list(self.schema_mapping.relationship_types.keys())

    def convert_enterprise_entity_to_graphiti(self, entity: 'Entity') -> Dict[str, Any]:
        """Convert enterprise Entity to Graphiti format."""
        return {
            'name': entity.name,
            'entity_type': entity.entity_type,
            'description': entity.description,
            'properties': entity.properties or {}
        }

    def convert_enterprise_relationship_to_graphiti(self, relationship: 'EntityRelationship') -> Dict[str, Any]:
        """Convert enterprise EntityRelationship to Graphiti format."""
        return {
            'subject': relationship.subject,
            'predicate': relationship.predicate,
            'object': relationship.object,
            'subject_type': relationship.subject_type,
            'object_type': relationship.object_type,
            'confidence_score': relationship.confidence_score,
            'context': relationship.context
        }
