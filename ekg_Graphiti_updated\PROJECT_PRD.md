# Product Requirements Document: Enterprise Knowledge Graph with Graphiti

**Version:** 1.0 (Based on current implementation as of May 30, 2025)

## 1. Introduction / Overview

The Enterprise Knowledge Graph (EKG) with Graphiti is a system designed to transform unstructured and semi-structured documents from various enterprise sources into a structured, interconnected knowledge graph. It leverages the Graphiti library for core knowledge graph operations, including entity and relationship extraction, semantic search, and community detection. The system aims to provide a robust and scalable solution for enterprises to unlock insights from their document repositories, making information more discoverable, traceable, and interconnected. It integrates with Neo4j for graph storage and utilizes LLM providers (configurable, with Requesty as a primary example) for advanced natural language understanding tasks. A key design principle is schema-awareness, ensuring that the extracted knowledge aligns with predefined enterprise ontologies.

## 2. Goals

*   **Centralize Enterprise Knowledge:** Ingest documents from various sources and formats to build a unified knowledge graph.
*   **Enable Semantic Understanding:** Go beyond keyword search to allow users to find information based on conceptual similarity and relationships.
*   **Ensure Data Traceability:** Provide clear lineage from extracted knowledge (entities, relationships) back to the original source documents and even specific chunks.
*   **Maintain Schema Compliance:** Adhere to enterprise-defined entity types, relationship types, and properties.
*   **Provide Flexible Configuration:** Allow customization of LLM providers, embedding strategies, document processing parameters, and search behavior.
*   **Offer a Developer-Friendly Workflow:** Provide tools and scripts for easy setup, document processing, and interaction with the knowledge graph.
*   **Facilitate Advanced Analytics:** Lay the foundation for complex graph queries, community detection, and discovery of implicit connections within the data.

## 3. Target Users

*   **Data Scientists/Analysts:** Users who need to query, analyze, and derive insights from the interconnected enterprise data.
*   **Knowledge Managers:** Individuals responsible for organizing and making enterprise information accessible.
*   **Software Developers/Engineers:** Teams building applications that leverage the knowledge graph for search, recommendation, or other intelligent features.
*   **Business Users (Indirectly):** Through applications built on top of the EKG, users who need to find relevant information quickly and efficiently.

## 4. Key Features & Functionality

### 4.1. Document Ingestion and Processing
*   **Multi-Format Support:** Ingests and processes documents in various formats:
    *   PDF (`.pdf`) using PyPDF2
    *   Microsoft Word (`.docx`) using `python-docx`
    *   Text (`.txt`)
    *   Markdown (`.md`)
    *   JSON (`.json`)
*   **Content Extraction:** Extracts textual content from supported file types.
*   **Intelligent Document Chunking:**
    *   Splits large documents into smaller, manageable chunks for LLM processing.
    *   Configurable `CHUNK_SIZE` and `CHUNK_OVERLAP` (defaults: 1000 and 200 characters respectively).
    *   Respects word boundaries to maintain semantic coherence.
    *   Configurable `MIN_CHUNK_SIZE` (default: 100 characters).
*   **Document Type Detection:** Attempts to classify document types based on filename patterns (e.g., 'meeting_notes', 'proposal', 'contract'). This can be overridden.
*   **Directory and Single File Processing:** Supports processing entire directories or individual files via CLI or programmatic access.
*   **Sample Document Creation:** Utility to generate sample documents for testing and demonstration.

### 4.2. Knowledge Extraction (via Graphiti & LLM)
*   **Entity Extraction:** Identifies and extracts named entities (e.g., Person, Project, Organization, Technology) from document chunks.
*   **Relationship Extraction:** Identifies and extracts relationships between entities, including the type of relationship and supporting facts.
*   **LLM Integration:**
    *   Utilizes an LLM client (configurable, e.g., Requesty via [`core/requesty_client.py`](enterprise_kg_graphiti/core/requesty_client.py:1)) for entity and relationship extraction.
    *   Supports configurable LLM providers and models (e.g., `LLM_PROVIDER=requesty`, `LLM_MODEL=openai/gpt-4o` or `anthropic/claude-3.5-sonnet`).
*   **Schema-Aware Extraction:**
    *   Integrates with an enterprise schema (defined in `enterprise_kg_minimal/constants/`) via a [`SchemaAdapter`](enterprise_kg_graphiti/core/schema_adapter.py:1).
    *   Validates extracted entity and relationship types against the defined schema.
    *   Enhances entities with predefined properties based on their type.

### 4.3. Knowledge Graph Storage (Neo4j)
*   **Graph Database:** Uses Neo4j as the backend for storing the knowledge graph.
*   **Node Types:**
    *   `Entity` nodes representing extracted entities.
    *   `EpisodicNode` (from Graphiti) representing document chunks, storing text and embeddings.
    *   `FileSource` nodes representing the original documents.
*   **Relationship Types:**
    *   Relationships between `Entity` nodes as extracted by the LLM.
    *   `CONTAINS` relationships linking `FileSource` nodes to the `Entity` and `EpisodicNode`s derived from them.
*   **Graphiti Core Integration:** Leverages Graphiti for managing graph elements, including episode creation, entity deduplication, and community updates.

### 4.4. Embedding Strategy
*   **Core Graphiti Requirement:** Embeddings are a fundamental part of the Graphiti framework for semantic search and community detection.
*   **SimpleEmbedder:**
    *   Default embedding provider ([`core/simple_embedder.py`](enterprise_kg_graphiti/core/simple_embedder.py:1)).
    *   Generates hash-based, deterministic embeddings locally without external API calls.
    *   Configurable embedding dimension (`EMBEDDING_DIM`, default: 384).
    *   Suitable for basic similarity matching, cost-effectiveness, and offline operation.
*   **Pinecone Integration (Optional Fallback):**
    *   Provides a `PineconeEmbedder` class that can use existing Pinecone embeddings, falling back to `SimpleEmbedder` if Pinecone is unavailable or not configured.
*   **Embedding Usage:**
    *   Document chunks (episodes) are embedded during processing.
    *   Search queries are embedded for semantic comparison.
    *   Used by Graphiti for community detection.

### 4.5. Search and Retrieval
*   **Hybrid Search:** Combines semantic similarity (via embeddings) with graph traversal and keyword matching. This is the default search type.
*   **Search Interface:** Provided by [`core/search_interface.py`](enterprise_kg_graphiti/core/search_interface.py:1) and integrated into the [`GraphitiEnterpriseProcessor`](enterprise_kg_graphiti/core/processor.py:1).
*   **Query Capabilities:**
    *   Search for content based on natural language queries.
    *   Retrieve entities, relationships, and document chunks.
*   **Configurable Search:**
    *   `SEARCH_DEFAULT_LIMIT` (default: 10)
    *   `SEARCH_MAX_LIMIT` (default: 100)
*   **Direct Cypher Queries:** Users can also query the Neo4j database directly for complex graph analysis.

### 4.6. Configuration Management
*   **Environment Variables:** Primary configuration method using a `.env` file (template: [`.env.template`](enterprise_kg_graphiti/.env.template:1)).
*   **Python Configuration Classes:** Structured configuration via dataclasses in [`config.py`](enterprise_kg_graphiti/config.py:1) (e.g., `Neo4jConfig`, `LLMConfig`, `EmbedderConfig`, `ProcessingConfig`, `SearchConfig`).
*   **Key Configurables:**
    *   Neo4j connection details (`NEO4J_URI`, `NEO4J_USER`, `NEO4J_PASSWORD`).
    *   LLM provider and model (`LLM_PROVIDER`, `LLM_MODEL`, API keys like `REQUESTY_API_KEY`).
    *   Embedder provider and model (`EMBEDDER_PROVIDER`, `EMBEDDER_MODEL`, `EMBEDDING_DIM`).
    *   Document processing (`CHUNK_SIZE`, `CHUNK_OVERLAP`, `MIN_CHUNK_SIZE`, `SUPPORTED_EXTENSIONS`).
    *   Graphiti settings (`GROUP_ID`, `UPDATE_COMMUNITIES`, `STORE_RAW_CONTENT`).
    *   Search settings.
*   **Configuration Validation:** The system includes a mechanism to validate the provided configuration.

### 4.7. Command-Line Interface (CLI)
*   **Main Entry Point:** [`main.py`](enterprise_kg_graphiti/main.py:1) provides CLI operations.
*   **Operations:**
    *   Process directories or single files.
    *   Create sample documents.
    *   Specify chunk size, overlap, and file extensions.
    *   Perform search queries after processing.
    *   Check configuration validity.
    *   Display schema information.
*   **Verbose/Quiet Modes:** Control logging output.

### 4.8. File Source Tracking and Traceability
*   **`FileSource` Nodes:** For each processed document, a `FileSource` node is created in Neo4j.
*   **Metadata:** `FileSource` nodes store metadata like filename, document type, total chunks, creation time, and file size.
*   **`CONTAINS` Relationships:** `FileSource` nodes are linked via `CONTAINS` relationships to the `Entity` nodes and `EpisodicNode`s extracted from them.
*   **Full Traceability:** Enables tracing any piece of knowledge in the graph back to its original document and chunk.

### 4.9. API Integrations
*   **Requesty API:** Integrated as an LLM provider for entity/relationship extraction. Configured via `REQUESTY_API_KEY` and `REQUESTY_BASE_URL`.
*   **OpenAI API (Optional):** Can be configured as an LLM or embedder provider.
*   **Other LLM Providers (Optional):** The system is designed to potentially support other providers like Anthropic or Google, as indicated in `.env` comments.

## 5. Technical Architecture

```mermaid
graph TB
    subgraph "Input Layer"
        A[Raw Documents .pdf, .docx, .txt, .md, .json] --> B[DocumentProcessor in main.py / core.processor.py]
    end

    subgraph "Processing Layer"
        B --> C{Content Extraction & Chunking}
        C --> D[ProcessedDocument & DocumentChunk Objects]
    end

    subgraph "Knowledge Graph Engine (Graphiti & Custom Logic)"
        D --> E[GraphitiEnterpriseProcessor]
        E --> F[SchemaAdapter]
        E --> G[LLM Client (e.g., RequestyClient)]
        E --> H[EmbedderClient (e.g., SimpleEmbedder)]
        F --> E
        G --> E
        H --> E
        E --> I[Graphiti Core: add_episode, entity/relationship extraction, embedding]
    end

    subgraph "Storage Layer"
        I --> J[Neo4j Database]
        J --> K[Entity Nodes]
        J --> L[Relationship Edges]
        J --> M[EpisodicNodes (Chunks with Embeddings)]
        J --> N[FileSource Nodes with CONTAINS Edges]
    end

    subgraph "Access & Retrieval Layer"
        O[User/Application] --> P[SearchInterface / CLI in main.py]
        P --> E
        P --> J
        O --> Q[Direct Neo4j Cypher Queries]
        Q --> J
        R[Hybrid Search Results] --> O
        S[Cypher Results] --> O
        E --> R
        J --> S
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style J fill:#ccf,stroke:#333,stroke-width:2px
```

**Key Components:**
*   **[`main.py`](enterprise_kg_graphiti/main.py:1):** CLI for user interaction, document processing initiation, and search.
*   **[`config.py`](enterprise_kg_graphiti/config.py:1):** Manages all system configurations.
*   **[`core/document_processor.py`](enterprise_kg_graphiti/core/document_processor.py:1):** Handles file reading, content extraction, and chunking.
*   **[`core/schema_adapter.py`](enterprise_kg_graphiti/core/schema_adapter.py:1):** Ensures adherence to the enterprise ontology.
*   **[`core/requesty_client.py`](enterprise_kg_graphiti/core/requesty_client.py:1):** LLM client for Requesty API.
*   **[`core/simple_embedder.py`](enterprise_kg_graphiti/core/simple_embedder.py:1):** Default local embedding provider.
*   **[`core/processor.py`](enterprise_kg_graphiti/core/processor.py:1) (`GraphitiEnterpriseProcessor`):** Orchestrates the entire workflow, integrating Graphiti, LLM, embedder, and schema.
*   **[`core/search_interface.py`](enterprise_kg_graphiti/core/search_interface.py:1):** Provides search functionalities.
*   **Graphiti Core Library:** Underlying engine for knowledge graph operations.
*   **Neo4j:** Graph database for persistent storage.
*   **[`end_to_end_flow.py`](enterprise_kg_graphiti/end_to_end_flow.py:1):** Demonstrates the complete workflow programmatically.

## 6. Data Model / Schema (Conceptual)

*   **FileSource Node:**
    *   Properties: `name` (filepath), `document_type`, `total_chunks`, `created_at`, `file_size`, `processing_method`.

*   **Entity Node (Graphiti `Entity`):**
    *   Properties: `name`, `type` (from enterprise schema, e.g., "Person", "Project"), `uuid`, `summary`, `description`, plus schema-defined enhanced properties.

*   **EpisodicNode (Graphiti `EpisodicNode`):**
    *   Represents a document chunk.
    *   Properties: `uuid`, `name` (e.g., `filename_chunk_X`), `content` (chunk text if `STORE_RAW_CONTENT` is true), `embedding` (vector), `reference_time`, `source_description`.

*   **Relationships (Graphiti `Relationship`):**
    *   Between `Entity` nodes.
    *   Properties: `type` (from enterprise schema, e.g., "leads", "responsible_for"), `fact` (textual description), `source_uuid`, `target_uuid`.

*   **`CONTAINS` Relationship:**
    *   Connects `FileSource` nodes to `Entity` nodes extracted from them.
    *   Connects `FileSource` nodes to `EpisodicNode`s (chunks) derived from them.

## 7. Non-Functional Requirements (Inferred)

*   **Configurability:** System parameters (LLM, embedding, chunking, Neo4j) are highly configurable via environment variables and Python classes.
*   **Traceability:** Full lineage from extracted knowledge back to source documents is maintained via `FileSource` nodes and `CONTAINS` relationships.
*   **Modularity:** Components like LLM client, embedder, and document processor are distinct, allowing for easier maintenance and potential replacement.
*   **Error Handling:** The system includes error logging and attempts graceful handling of issues during document processing and API interactions (as seen in `END_TO_END_FLOW_ANALYSIS.md` and `test_requesty_api.py`).
*   **Developer Experience:** Includes quick start guides, end-to-end flow examples, setup tests, and CLI tools to facilitate development and usage.
*   **Consistency (with SimpleEmbedder):** The default embedding strategy ensures deterministic embeddings, which is beneficial for reproducibility and offline scenarios.

## 8. Assumptions & Dependencies

*   **Python Environment:** Python 3.8+ is required.
*   **Neo4j Database:** A running and accessible Neo4j instance is necessary. Credentials must be provided.
*   **LLM API Access:**
    *   For Requesty: A valid `REQUESTY_API_KEY` and accessible `REQUESTY_BASE_URL`.
    *   For other providers (if configured): Corresponding API keys and endpoint access.
*   **Dependencies:** Python packages listed in [`requirements.txt`](enterprise_kg_graphiti/requirements.txt:1) must be installed (e.g., `graphiti-core`, `neo4j`, `python-dotenv`, `PyPDF2`, `python-docx`).
*   **Enterprise Schema:** The system assumes the availability and correctness of schema definitions (entity types, relationship types) from the `enterprise_kg_minimal/constants/` directory structure.
*   **File System Access:** The system needs read access to document directories/files and write access for logs or sample document creation.
*   **Network Access:** Required for connecting to Neo4j and external LLM/embedding APIs (if not using `SimpleEmbedder`).